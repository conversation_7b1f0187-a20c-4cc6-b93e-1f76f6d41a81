# Analytics Models Comprehensive Documentation


---

## 📊 **MODEL ARCHITECTURE OVERVIEW**

### **Analytics Architecture**

![Analytics Model Architecture](AnalyticsModel.png)

### **Model Specialization**

| Model | Purpose | Records | Storage | Performance | Business Focus |
|-------|---------|---------|---------|-------------|----------------|
| **mv_lead_primary_flow_data** | Lead-centric email analytics | ~1.4M | ~500MB | **EXCELLENT** | Email sequences, replies, attribution |
| **mv_meetings_primary_flow_data** | Meeting-centric analytics | ~1.5K | ~50MB | **EXCELLENT** | Meeting conversion, booking rates |
| **mv_lead_analytics_cube** | Aggregated business intelligence | ~170K | ~100MB | **EXCELLENT** | Multi-dimensional analysis |

---

## 🔍 **DATA SOURCES & FIELD ANALYSIS**

### **1. Email Data Sources**

#### **mv_email_with_thread_start**
**Purpose**: Core email data with thread analysis and reply classification

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `id` | INT | Unique email identifier | Primary key for joins |
| `leadId` | INT | Lead identifier | Helps in aggregates |
| `body` | TEXT | Email content | Reply classification |
| `type` | TEXT | Email type (SENT/REPLY/FORWARD) | Core business metric |
| `toEmailID` | TEXT | Recipient email | Prospect identification |
| `fromEmailID` | TEXT | Sender email | Inbox identification |
| `time` | TIMESTAMP | Email timestamp | Temporal analysis |
| `campaingId` | INT | Campaign identifier | Campaign attribution |
| `email_seq_number` | TEXT | Sequence number (1,2,3,null) | Sequence analysis |
| `open_count` | INT | Email opens | Engagement metric |
| `threadStartEmailId` | TEXT | Thread start email | Stable Inbox identification |
| `threadToEmailId` | TEXT | Thread recipient | Stable Lead identification |
| `email_category` | TEXT | Prospect type (work/personal/role/unknown) | Segmentation |
| `reply_classification` | TEXT | Reply type (AUTOMATED/ERROR/UNSUBSCRIBE/HUMAN) | Quality analysis |
| `jeff_email_status` | TEXT | Business status (REPLY/REPLY_CS_AUTOMATED/ERROR_REPLY/SENT) | Business logic |

**Join Strategy**: 
- **Thread-based joins**: Links emails in conversation threads
- **Time-based sequencing**: Maintains email sequence order
- **Lead-level aggregation**: Groups by leadId for sequence flags

### **2. Meeting Data Sources**

#### **MeetingsFromReplit**
**Purpose**: Raw meeting data from Replit integration

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `fromEmailId` | TEXT | Meeting sender | Inbox identification |
| `toEmailId` | TEXT | Meeting recipient | Lead identification |
| `date` | DATE | Meeting date | Temporal attribution |
| `client` | INT | Client ID | Client attribution |
| `funnel` | TEXT | Campaign funnel | Campaign attribution |
| `slots_sent_after_1` | INT | Slots sent after email 1 | Sequence attribution |
| `slots_sent_after_2` | INT | Slots sent after email 2 | Sequence attribution |
| `slots_sent_after_3` | INT | Slots sent after email 3 | Sequence attribution |
| `slots_sent_unknown` | INT | Slots sent (unknown sequence) | Fallback attribution |
| `booked` | INT | Meetings actually booked | Conversion metric |

### **3. Campaign Data Sources**

#### **Campaign Table**
**Purpose**: Campaign metadata and hierarchy

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `campaignId` | INT | Unique campaign identifier | Primary key |
| `name` | TEXT | Campaign name | Campaign identification |
| `clientId` | INT | Client identifier | Client attribution |
| `parentCampaignId` | INT | Parent campaign | Campaign hierarchy |

**Business Logic**:
```sql
-- Jeff Campaign Code Extraction
CASE
  WHEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
  THEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]')
  WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
  ELSE 'UNK'
END AS jeff_campaign_code

-- Campaign Series Extraction
COALESCE(
  substring(
    substring(c."name" FROM '([0-9]+[A-Z])') 
    FROM '([0-9]+)'
  ),
  'UNK'
) AS campaign_series
```

### **4. Client Data Sources**

#### **Client Table**
**Purpose**: Client information and hierarchy

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `clientId` | INT | Unique client identifier | Primary key |
| `name` | TEXT | Client name | Client identification |
| `businessName` | TEXT | Business name | Business identification |

**Business Logic**:
```sql
-- Client Name Resolution
COALESCE(c."businessName", c."name") as client_name
```

### **5. Prospect Data Sources**

#### **seller_group_sellers_with_sp_classifications_prospects_w_emails**
**Purpose**: Prospect enrichment with search priorities and email classifications

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `email` | TEXT | Prospect email | Lead identification |
| `emailProvider` | TEXT | Email provider | Provider analysis |
| `jeff_search_priority` | TEXT | Search priority | Lead quality |
| `dominant_search_priority` | TEXT | Dominant priority | Lead classification |
| `email_type` | TEXT | Email type classification | Segmentation |

**Business Logic**:
```sql
-- Prospect Parent Provider Classification
CASE 
  WHEN sp271."emailProvider" ~* '(gmail|google)' THEN 'Google'
  WHEN sp271."emailProvider" ~* '(outlook|hotmail|live|microsoft)' THEN 'Outlook'  
  WHEN sp271."emailProvider" ~* 'zoho' THEN 'Zoho'
  WHEN sp271."emailProvider" ~* 'yahoo' THEN 'Yahoo'
  WHEN sp271."emailProvider" ~* 'aol' THEN 'AOL'
  WHEN sp271."emailProvider" = '' OR sp271."emailProvider" IS NULL THEN 'Unknown'
  ELSE 'Others'
END as prospect_parent_provider
```

### **6. Inbox Data Sources**

#### **InboxesFromReplit**
**Purpose**: Inbox provider information and configuration

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `inboxEmail` | TEXT | Inbox email address | Inbox identification |
| `provider_name` | TEXT | Provider name | Provider analysis |
| `domain` | TEXT | Domain name | Domain analysis |
| `id` | INT | Inbox identifier | Primary key |

#### **InboxTagsFromReplit**
**Purpose**: Tag-based inbox organization

| Field | Type | Significance | Business Value |
|-------|------|-------------|----------------|
| `inboxId` | INT | Inbox identifier | Foreign key |
| `tag` | TEXT | Tag name | Campaign organization |

**Business Logic**:
```sql
-- Tag Aggregation
ARRAY_AGG(t."tag") as tag_names

-- Inbox Parent Provider Classification
CASE 
  WHEN i."provider_name" ~* '(gmail|google)' THEN 'Google'
  WHEN i."provider_name" ~* '(outlook|hotmail|live|microsoft)' THEN 'Outlook'
  WHEN i."provider_name" ~* 'zoho' THEN 'Zoho'  
  WHEN i."provider_name" ~* 'yahoo' THEN 'Yahoo'
  WHEN i."provider_name" ~* 'aol' THEN 'AOL'
  WHEN i."provider_name" = '' OR i."provider_name" IS NULL THEN 'Unknown'
  ELSE 'Others'
END as inbox_parent_provider
```

---

## 🔗 **JOIN STRATEGIES & BUSINESS LOGIC**

### **1. Lead Primary Flow Data Join Strategy**

#### **Core Join Logic**
```sql
FROM email_classification ec
LEFT JOIN amazon_prospect_enrichment ape ON LOWER(TRIM(ec."threadToEmailId")) = LOWER(TRIM(ape.prospect_email))
LEFT JOIN inbox_enrichment ie ON LOWER(TRIM(ec."threadStartEmailId")) = LOWER(TRIM(ie."inboxEmail"))
LEFT JOIN campaign_parsing cp ON ec."campaingId" = cp."campaignId"
LEFT JOIN client_info ci ON cp."clientId" = ci."clientId"
LEFT JOIN meeting_data md ON 
  cp.jeff_campaign_code = md."funnel"
  AND md."client" = cp."clientId"
  AND ec."threadStartEmailId" = md."fromEmailId"
  AND ec."threadToEmailId" = md."toEmailId"
  -- CRITICAL: Time-based attribution window
  AND md."date" >= ec."time"
  AND (ec.next_email_time IS NULL OR md."date" < ec.next_email_time)
```

#### **Advanced Business Logic**

**1. Lead Email Sequence Flags**
```sql
-- Pre-calculate sequence flags for proper reply attribution
lead_email_sequence_flags AS (
  SELECT 
    "leadId",
    MAX(CASE WHEN email_seq_number = '1' AND type = 'SENT' THEN 1 ELSE 0 END) AS has_email_1_sent,
    MAX(CASE WHEN email_seq_number = '2' AND type = 'SENT' THEN 1 ELSE 0 END) AS has_email_2_sent,
    MAX(CASE WHEN email_seq_number = '3' AND type = 'SENT' THEN 1 ELSE 0 END) AS has_email_3_sent
  FROM public.mv_email_with_thread_start
  GROUP BY "leadId"
)
```

**2. Advanced Reply Attribution**
```sql
-- Lead-level reply attribution (more accurate than email-level)
CASE WHEN ec.is_reply = 1 AND ec.has_email_1_sent = 1 THEN 1 ELSE 0 END AS replies_after_email_1
```

**3. Time-Based Meeting Attribution**
```sql
-- Meeting must occur after email was sent
AND md."date" >= ec."time"
-- Meeting must occur before next email was sent
AND (ec.next_email_time IS NULL OR md."date" < ec.next_email_time)
```

**4. Email Sequence Classification**
```sql
-- Business metrics by sequence
CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_1_sent
CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_2_sent
CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_3_sent
```

### **2. Meetings Primary Flow Data Join Strategy**

#### **Core Join Logic**
```sql
FROM "public2"."MeetingsFromReplit" AS meetings 
LEFT JOIN campaign_parsing cp ON meetings."client" = cp."clientId" AND meetings."funnel" = cp."jeff_campaign_code"
LEFT JOIN email_classification ec ON meetings."fromEmailId" = ec."threadStartEmailId" 
  AND meetings."toEmailId" = ec."threadToEmailId" 
  -- CRITICAL: Time-based attribution window
  AND meetings."date" >= ec."time"
  AND (ec.next_email_time IS NULL OR meetings."date" < ec.next_email_time)
LEFT JOIN amazon_prospect_enrichment ape ON LOWER(TRIM(meetings."toEmailId")) = LOWER(TRIM(ape.prospect_email))
LEFT JOIN inbox_enrichment ie ON LOWER(TRIM(meetings."fromEmailId")) = LOWER(TRIM(ie."inboxEmail"))
LEFT JOIN client_info ci ON meetings."client" = ci."clientId"
```

#### **Deduplication Strategy**
```sql
-- Ensure one record per meeting
SELECT DISTINCT ON (meetings."fromEmailId", meetings."toEmailId", meetings."date", meetings."client", meetings."funnel")
```

### **3. Lead Analytics Cube Join Strategy**

#### **Aggregation Logic**
```sql
FROM public.mv_lead_primary_flow_data
GROUP BY 
  week_start_date,
  prospect_email_provider,
  dominant_search_priority,
  prospect_parent_provider,
  campaign_name,
  jeff_campaign_code,
  inbox_provider_name,
  inbox_domain_name,
  inbox_parent_provider,
  tag_names,
  client_name
```

#### **Advanced Metrics Calculation**
```sql
-- Unique lead counts within dimension combinations
COUNT(DISTINCT "leadId") as unique_leads_contacted

-- Calculated rates with proper aggregation
CASE WHEN SUM(email_1_sent) > 0 
  THEN ROUND(100.0 * SUM(replies_after_email_1) / SUM(email_1_sent), 2) 
  ELSE 0 END as email_1_reply_rate
```

---

## 📊 **COMPREHENSIVE METRICS ANALYSIS**

### **1. Email Metrics**

#### **Email Sent Metrics**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `email_1_sent` | First sequence emails | High - Initial outreach | `CASE WHEN email_seq_number = '1' AND type = 'SENT' THEN 1 ELSE 0 END` |
| `email_2_sent` | Second sequence emails | High - Follow-up | `CASE WHEN email_seq_number = '2' AND type = 'SENT' THEN 1 ELSE 0 END` |
| `email_3_sent` | Third sequence emails | Medium - Final follow-up | `CASE WHEN email_seq_number = '3' AND type = 'SENT' THEN 1 ELSE 0 END` |
| `total_emails_sent` | Total emails sent | High - Overall volume | `SUM(email_1_sent + email_2_sent + email_3_sent)` |

#### **Email Sent by Prospect Type**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `email_1_sent_personal` | Personal emails (sequence 1) | High - Personalization | `CASE WHEN email_seq_number = '1' AND email_category = 'personal' THEN 1 ELSE 0 END` |
| `email_1_sent_role` | Role-based emails (sequence 1) | High - Targeting | `CASE WHEN email_seq_number = '1' AND email_category = 'role' THEN 1 ELSE 0 END` |
| `email_1_sent_work` | Work emails (sequence 1) | High - Business targeting | `CASE WHEN email_seq_number = '1' AND email_category = 'work' THEN 1 ELSE 0 END` |
| `email_1_sent_unknown` | Unknown type emails (sequence 1) | Medium - Fallback | `CASE WHEN email_seq_number = '1' AND email_category IS NULL THEN 1 ELSE 0 END` |

### **2. Reply Metrics**

#### **Reply Attribution Metrics**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `replies_after_email_1` | Replies after first email | High - Initial engagement | `CASE WHEN is_reply = 1 AND has_email_1_sent = 1 THEN 1 ELSE 0 END` |
| `replies_after_email_2` | Replies after second email | Medium - Follow-up engagement | `CASE WHEN is_reply = 1 AND has_email_2_sent = 1 THEN 1 ELSE 0 END` |
| `replies_after_email_3` | Replies after third email | Low - Final engagement | `CASE WHEN is_reply = 1 AND has_email_3_sent = 1 THEN 1 ELSE 0 END` |
| `total_replies` | Total replies | High - Overall engagement | `SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3)` |

#### **Reply Classification Metrics**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `automated_replies_after_email_1` | Automated replies (sequence 1) | Medium - Quality monitoring | `CASE WHEN is_auto_reply = 1 AND has_email_1_sent = 1 THEN 1 ELSE 0 END` |
| `error_replies_after_email_1` | Error replies (sequence 1) | High - Quality monitoring | `CASE WHEN is_error_reply = 1 AND has_email_1_sent = 1 THEN 1 ELSE 0 END` |

### **3. Meeting Metrics**

#### **Meeting Attribution Metrics**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `meeting_slots_sent_after_1` | Slots sent after email 1 | High - Conversion opportunity | `SUM(COALESCE(meetings_slots_sent_after_1, 0))` |
| `meeting_slots_sent_after_2` | Slots sent after email 2 | Medium - Follow-up conversion | `SUM(COALESCE(meetings_slots_sent_after_2, 0))` |
| `meeting_slots_sent_after_3` | Slots sent after email 3 | Low - Final conversion | `SUM(COALESCE(meetings_slots_sent_after_3, 0))` |
| `meetings_booked` | Actual meetings booked | High - Actual conversion | `SUM(COALESCE(meetings_booked, 0))` |

### **4. Performance Metrics**

#### **Reply Rate Calculations**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `email_1_reply_rate` | Reply rate for email 1 | High - Sequence performance | `ROUND(100.0 * SUM(replies_after_email_1) / SUM(email_1_sent), 2)` |
| `email_2_reply_rate` | Reply rate for email 2 | Medium - Follow-up performance | `ROUND(100.0 * SUM(replies_after_email_2) / SUM(email_2_sent), 2)` |
| `email_3_reply_rate` | Reply rate for email 3 | Low - Final performance | `ROUND(100.0 * SUM(replies_after_email_3) / SUM(email_3_sent), 2)` |
| `overall_reply_rate` | Overall reply rate | High - Overall performance | `ROUND(100.0 * SUM(total_replies) / SUM(total_emails_sent), 2)` |

#### **Conversion Rate Calculations**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `booking_rate` | Meeting booking rate | High - Conversion efficiency | `ROUND(100.0 * SUM(meetings_booked) / SUM(total_meeting_slots_sent), 2)` |
| `lead_reply_rate` | Lead-level reply rate | High - Lead engagement | `ROUND(100.0 * COUNT(DISTINCT replied_leads) / COUNT(DISTINCT contacted_leads), 2)` |
| `lead_meeting_rate` | Lead-level meeting rate | High - Lead conversion | `ROUND(100.0 * COUNT(DISTINCT meeting_leads) / COUNT(DISTINCT contacted_leads), 2)` |

### **5. Unique Lead Metrics**

#### **Lead Count Metrics**
| Metric | Description | Business Value | Calculation |
|--------|-------------|----------------|-------------|
| `unique_leads_contacted` | Unique leads contacted | High - Reach metrics | `COUNT(DISTINCT "leadId")` |
| `unique_leads_replied` | Unique leads that replied | High - Engagement metrics | `COUNT(DISTINCT CASE WHEN replied THEN "leadId" END)` |
| `unique_leads_with_meetings` | Unique leads with meetings | High - Conversion metrics | `COUNT(DISTINCT CASE WHEN meetings_booked > 0 THEN "leadId" END)` |

---

## 🎯 **DIMENSIONS & ANALYTICAL CAPABILITIES**

### **1. Temporal Dimensions**

| Dimension | Type | Values | Business Value | Usage |
|-----------|------|--------|----------------|-------|
| `week_start_date` | DATE | Sunday-based weeks | High - Trend analysis | Weekly performance tracking |
| `email_date` | DATE | Daily granularity | Medium - Daily analysis | Daily performance monitoring |
| `meetings_date` | DATE | Meeting dates | High - Conversion timing | Meeting attribution |

### **2. Prospect Dimensions**

| Dimension | Type | Values | Business Value | Usage |
|-----------|------|--------|----------------|-------|
| `prospect_email_provider` | TEXT | gmail.com, outlook.com, etc. | High - Provider analysis | Provider performance comparison |
| `prospect_parent_provider` | TEXT | Google, Outlook, Yahoo, etc. | High - Provider grouping | Provider category analysis |
| `dominant_search_priority` | TEXT | SP1, SP2, SP3, etc. | High - Lead quality | Lead quality analysis |
| `prospect_email_type` | TEXT | work, personal, role, unknown | High - Targeting analysis | Segmentation analysis |

### **3. Campaign Dimensions**

| Dimension | Type | Values | Business Value | Usage |
|-----------|------|--------|----------------|-------|
| `campaign_name` | TEXT | Campaign names | High - Campaign analysis | Campaign performance |
| `jeff_campaign_code` | TEXT | 6C, 7A, 8B, etc. | High - Funnel analysis | Funnel performance |
| `campaign_series` | TEXT | 6, 7, 8, etc. | High - Series analysis | Series performance |
| `client_name` | TEXT | Client names | High - Client analysis | Client performance |

### **4. Inbox Dimensions**

| Dimension | Type | Values | Business Value | Usage |
|-----------|------|--------|----------------|-------|
| `inbox_provider_name` | TEXT | Provider names | High - Inbox performance | Inbox efficiency analysis |
| `inbox_domain_name` | TEXT | Domain names | Medium - Domain analysis | Domain-specific analysis |
| `inbox_parent_provider` | TEXT | Google, Outlook, etc. | High - Provider grouping | Provider category analysis |
| `tag_names` | ARRAY | Tag arrays | High - Campaign organization | Tag-based analysis |

### **5. Email Classification Dimensions**

| Dimension | Type | Values | Business Value | Usage |
|-----------|------|--------|----------------|-------|
| `email_seq_number` | TEXT | 1, 2, 3, null | High - Sequence analysis | Sequence performance |
| `jeff_email_status` | TEXT | SENT, REPLY, ERROR_REPLY | High - Status analysis | Email status tracking |
| `reply_classification` | TEXT | HUMAN, AUTOMATED, ERROR | High - Quality analysis | Reply quality analysis |

---

## 🔍 **ANOMALY DETECTION SYSTEM**

### **1. Data Consistency Monitoring**

#### **Campaign Code Mismatch Detection**
```sql
-- Identifies meetings with mismatched campaign codes
WITH meeting_email_analysis AS (
  SELECT 
    meetings."funnel" as meeting_funnel,
    cp.jeff_campaign_code as email_campaign_code,
    CASE 
      WHEN meetings."funnel" = cp.jeff_campaign_code THEN 'MATCH'
      ELSE 'MISMATCH'
    END as consistency_status
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN campaign_parsing cp ON e."campaingId" = cp."campaignId"
)
```

**Key Metrics**:
- **Mismatch Percentage**: Percentage of meetings with inconsistent campaign codes
- **Affected Clients**: Number of clients impacted by mismatches
- **Affected Campaigns**: Number of campaigns with mismatches

#### **Lead Model vs Non-Orphaned Meetings Comparison**
```sql
-- Compares lead model meetings with non-orphaned meetings
WITH non_orphaned_meetings AS (
  SELECT 
    meetings."funnel",
    COUNT(*) as total_meetings,
    SUM(meetings.booked) as total_booked
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN (
    SELECT DISTINCT e."threadStartEmailId", e."threadToEmailId"
    FROM public.mv_email_with_thread_start e
  ) emails ON meetings."fromEmailId" = emails."threadStartEmailId" 
    AND meetings."toEmailId" = emails."threadToEmailId"
  GROUP BY meetings."funnel"
)
```

**Key Metrics**:
- **Missing Meetings**: Difference between expected and actual meetings
- **Missing Booked**: Difference between expected and actual bookings
- **Status**: EXACT_MATCH or MISMATCH

### **2. Data Quality Monitoring**

#### **Daily Anomaly Detection**
```sql
-- Monitors new anomalies as they appear
WITH recent_anomalies AS (
  SELECT 
    meetings."date",
    meetings."funnel" as meeting_funnel,
    cp.jeff_campaign_code as email_campaign_code,
    CASE 
      WHEN meetings."funnel" = cp.jeff_campaign_code THEN 'MATCH'
      ELSE 'MISMATCH'
    END as consistency_status
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN campaign_parsing cp ON e."campaingId" = cp."campaignId"
  WHERE meetings."date" >= CURRENT_DATE - INTERVAL '7 days'
)
```

**Key Metrics**:
- **Daily Mismatch Count**: Number of mismatches per day
- **Campaign Impact**: Which campaigns are most affected
- **Trend Analysis**: Mismatch trends over time

### **3. Root Cause Analysis**

#### **Campaign Mapping Issues**
```sql
-- Identifies which campaigns cause the most inconsistencies
WITH campaign_mapping_issues AS (
  SELECT 
    meetings."funnel" as meeting_funnel,
    meetings."client" as meeting_client,
    e."campaingId" as email_campaign_id,
    cp.jeff_campaign_code as email_campaign_code,
    cp.campaign_name,
    COUNT(*) as meeting_count,
    SUM(meetings.booked) as booked_count
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN campaign_parsing cp ON e."campaingId" = cp."campaignId"
  WHERE meetings."funnel" != cp.jeff_campaign_code
  GROUP BY meetings."funnel", meetings."client", e."campaingId", cp.jeff_campaign_code, cp.campaign_name
)
```

**Key Metrics**:
- **Top Problematic Campaigns**: Campaigns with most mismatches
- **Client Impact**: Which clients are most affected
- **Meeting Volume Impact**: Volume of meetings affected

---

## 📈 **BUSINESS CAPABILITIES & USE CASES**

### **1. Lead Primary Flow Data Capabilities**

#### **Email Sequence Analysis**
- **Reply Rate by Sequence**: Compare performance across email sequences
- **Sequence Effectiveness**: Identify which sequences drive most engagement
- **Sequence Timing**: Analyze optimal timing between sequences
- **Sequence Personalization**: Compare performance by prospect type

#### **Lead-Level Attribution**
- **Lead Engagement Tracking**: Track which leads engage across sequences
- **Lead Conversion Funnel**: Analyze lead progression through sequences
- **Lead Quality Analysis**: Compare performance by lead quality indicators
- **Lead Response Patterns**: Identify common response patterns

#### **Campaign Performance Analysis**
- **Campaign Comparison**: Compare performance across campaigns
- **Series Performance**: Analyze performance by campaign series
- **Client Performance**: Compare performance across clients
- **Funnel Analysis**: Analyze performance by funnel codes

### **2. Meetings Primary Flow Data Capabilities**

#### **Meeting Conversion Analysis**
- **Booking Rate Analysis**: Analyze meeting booking rates by various dimensions
- **Meeting Attribution**: Attribute meetings to specific email sequences
- **Meeting Timing**: Analyze optimal timing for meeting requests
- **Meeting Quality**: Analyze meeting quality by various factors

#### **Meeting Performance Metrics**
- **Meeting Volume**: Track meeting volume across dimensions
- **Meeting Conversion**: Analyze conversion from slots to bookings
- **Meeting Efficiency**: Analyze meeting efficiency by provider/client
- **Meeting Trends**: Track meeting trends over time

### **3. Lead Analytics Cube Capabilities**

#### **Multi-Dimensional Analysis**
- **Provider Performance**: Compare performance across email providers
- **Prospect Type Analysis**: Analyze performance by prospect types
- **Campaign Performance**: Compare performance across campaigns
- **Client Performance**: Analyze performance across clients

#### **Aggregated Business Intelligence**
- **Executive Dashboards**: High-level performance metrics
- **Trend Analysis**: Performance trends across dimensions
- **Comparative Analysis**: Compare performance across segments
- **Predictive Analytics**: Identify patterns for future performance

---

# Field Generation Analysis: Old vs New Flow

## Overview
This document compares how each metric is generated in the old working flow vs the new 9D flow to identify discrepancies.

## Data Flow Comparison

### OLD FLOW (Working)
```
mv_email_with_thread_start 
→ 277-smartlead-emails-with-custom-reply-status 
→ 278-smartlead-analytics 
→ Final Dashboard Queries
```

### NEW FLOW (Broken)
```
mv_email_with_thread_start 
→ mv-email-enriched-9d 
→ mv-analytics-cube-9d 
→ funnel-client-analytics 
→ Final Dashboard Queries
```

---

## Field Generation Analysis

### 1. **Email 1 Sent Count**

#### OLD FLOW (278-smartlead-analytics.sql)
```sql
-- Step 1: Create individual flags per email record
CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_1_sent

-- Step 2: Sum flags by date/client/campaign
SUM(eab.email_1_sent) AS email_1_sent_count
```

#### NEW FLOW (funnel-client-analytics.sql)
```sql
-- Step 1: Pre-aggregate in cube by all dimensions
SUM(CASE WHEN em.email_seq_number = '1' AND em.email_action = 'SENT' 
    THEN em.event_count ELSE 0 END) as email_1_sent

-- Step 2: Sum again in final query
SUM(email_1_sent) as email_1_sent
```

**🚨 ISSUE**: Double aggregation + over-granular grouping in cube

---

### 2. **Email 1 Sent Personal Count**

#### OLD FLOW
```sql
-- Step 1: Create flags with prospect type filter
CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
     AND emails."prospect_email_type" = 'personal' THEN 1 ELSE 0 END AS email_1_sent_personal

-- Step 2: Sum flags
SUM(eab.email_1_sent_personal) AS email_1_sent_personal_count
```

#### NEW FLOW
```sql
-- Step 1: Pre-aggregate in cube with prospect type filter
SUM(CASE WHEN em.email_seq_number = '1' AND em.email_action = 'SENT' 
     AND em.prospect_email_type = 'personal' THEN em.event_count ELSE 0 END) as email_1_sent_personal

-- Step 2: Sum again in final query
SUM(email_1_sent_personal) as email_1_sent_personal
```

**🚨 ISSUE**: Same double aggregation problem

---

### 3. **Email 1 Sent Role Count**

#### OLD FLOW
```sql
CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
     AND emails."prospect_email_type" = 'role' THEN 1 ELSE 0 END AS email_1_sent_role
SUM(eab.email_1_sent_role) AS email_1_sent_role_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '1' AND em.email_action = 'SENT' 
     AND em.prospect_email_type = 'role' THEN em.event_count ELSE 0 END) as email_1_sent_role
SUM(email_1_sent_role) as email_1_sent_role
```

---

### 4. **Email 1 Sent Work Count**

#### OLD FLOW
```sql
CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
     AND emails."prospect_email_type" = 'work' THEN 1 ELSE 0 END AS email_1_sent_work
SUM(eab.email_1_sent_work) AS email_1_sent_work_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '1' AND em.email_action = 'SENT' 
     AND em.prospect_email_type = 'work' THEN em.event_count ELSE 0 END) as email_1_sent_work
SUM(email_1_sent_work) as email_1_sent_work
```

---

### 5. **Email 1 Sent Unknown Count**

#### OLD FLOW
```sql
CASE WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
     AND (emails."prospect_email_type" IS NULL OR emails."prospect_email_type" = '') 
     THEN 1 ELSE 0 END AS email_1_sent_unknown
SUM(eab.email_1_sent_unknown) AS email_1_sent_unknown_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '1' AND em.email_action = 'SENT' 
     AND (em.prospect_email_type = 'unknown' OR em.prospect_email_type IS NULL OR em.prospect_email_type = '') 
     THEN em.event_count ELSE 0 END) as email_1_sent_unknown
SUM(email_1_sent_unknown) as email_1_sent_unknown
```

**🚨 ISSUE**: Different NULL handling logic

---

### 6. **Replies After Email 1 Count**

#### OLD FLOW
```sql
-- Step 1: Create reply flags
CASE WHEN emails."jeff_email_status" = 'REPLY' THEN 1 ELSE 0 END AS is_reply

-- Step 2: Pre-calculate lead sequence flags
WITH lead_sequences AS (
  SELECT "leadId", MAX(email_1_sent) AS has_email_1_sent
  FROM email_analytics_base GROUP BY "leadId"
)

-- Step 3: Count replies only from leads that sent email 1
SUM(CASE WHEN eab.is_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_1_count
```

#### NEW FLOW
```sql
-- Step 1: Pre-aggregate in cube (WRONG LOGIC)
SUM(CASE WHEN em.email_seq_number = '1' AND em.reply_classification = 'HUMAN' 
    THEN em.event_count ELSE 0 END) as replies_after_email_1

-- Step 2: Sum again in final query
SUM(replies_after_email_1) as replies_after_email_1
```

**🚨 CRITICAL ISSUE**: 
- Old: Counts replies from leads who SENT email 1
- New: Counts replies that happen to have seq_number = '1'

---

### 7. **Automated Replies After Email 1 Count**

#### OLD FLOW
```sql
CASE WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' THEN 1 ELSE 0 END AS is_auto_reply
SUM(CASE WHEN eab.is_auto_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS automated_replies_after_email_1_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '1' AND em.reply_classification = 'AUTOMATED' 
    THEN em.event_count ELSE 0 END) as automated_replies_after_email_1
SUM(automated_replies_after_email_1) as automated_replies_after_email_1
```

**🚨 ISSUE**: Same wrong attribution logic + different classification system

---

### 8. **Error Replies After Email 1 Count**

#### OLD FLOW
```sql
CASE WHEN emails."jeff_email_status" = 'ERROR_REPLY' THEN 1 ELSE 0 END AS is_error_reply
SUM(CASE WHEN eab.is_error_reply = 1 AND ls.has_email_1_sent = 1 THEN 1 ELSE 0 END) AS error_replies_after_email_1_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '1' AND em.reply_classification = 'ERROR' 
    THEN em.event_count ELSE 0 END) as error_replies_after_email_1
SUM(error_replies_after_email_1) as error_replies_after_email_1
```

---

### 9. **Meeting Slots Sent After 1**

#### OLD FLOW
```sql
-- Direct join to meeting data
LEFT JOIN "public2"."MeetingsFromReplit" AS meetings 
  ON eab."clientSmartLeadId" = meetings."client" 
  AND eab.email_date = meetings."date"
  AND eab."jeff_campaign_code" = meetings."funnel"
  AND eab."threadStartEmailId" = meetings."fromEmailId"

SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) AS meeting_slots_sent_after_1
```

#### NEW FLOW
```sql
-- Pre-aggregated in cube from enriched view
SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1
```

**⚠️ POTENTIAL ISSUE**: Different meeting attribution logic

---

### 10. **Email 2 Sent Count**

#### OLD FLOW
```sql
CASE WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_2_sent
SUM(eab.email_2_sent) AS email_2_sent_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '2' AND em.email_action = 'SENT' 
    THEN em.event_count ELSE 0 END) as email_2_sent
SUM(email_2_sent) as email_2_sent
```

---

### 11. **Email 3 Sent Count**

#### OLD FLOW
```sql
CASE WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' THEN 1 ELSE 0 END AS email_3_sent
SUM(eab.email_3_sent) AS email_3_sent_count
```

#### NEW FLOW
```sql
SUM(CASE WHEN em.email_seq_number = '3' AND em.email_action = 'SENT' 
    THEN em.event_count ELSE 0 END) as email_3_sent
SUM(email_3_sent) as email_3_sent
```

---

### 12. **Total Meeting Slots Sent**

#### OLD FLOW
```sql
(SUM(DISTINCT COALESCE(meetings.slots_sent_after_1, 0)) + 
 SUM(DISTINCT COALESCE(meetings.slots_sent_after_2, 0)) + 
 SUM(DISTINCT COALESCE(meetings.slots_sent_after_3, 0)) + 
 SUM(DISTINCT COALESCE(meetings.slots_sent_unknown, 0))) AS total_meeting_slots_sent
```

#### NEW FLOW
```sql
SUM(total_meeting_slots_sent) as total_meeting_slots_sent
```

---

### 13. **Meetings Booked**

#### OLD FLOW
```sql
SUM(DISTINCT COALESCE(meetings.booked, 0)) AS meetings_booked
```

#### NEW FLOW
```sql
SUM(meetings_booked) as meetings_booked
```

---

## Key Differences Summary

### 🚨 **CRITICAL ISSUES**

1. **Reply Attribution Logic**
   - **Old**: Counts replies from leads who SENT that email sequence
   - **New**: Counts replies that happen to have that sequence number
   - **Impact**: All reply metrics are wrong

2. **Double Aggregation**
   - **Old**: Single aggregation from email records
   - **New**: Pre-aggregation in cube + final aggregation
   - **Impact**: Email sent counts may be inflated

3. **Prospect Email Type Source**
   - **Old**: Uses `seller_group_sellers_with_sp_classifications_prospects_w_emails`
   - **New**: Calculates inline from `AmazonProspect`
   - **Impact**: Email type breakdowns may differ

4. **Reply Classification System**
   - **Old**: Uses `jeff_email_status` with extensive pattern matching
   - **New**: Uses simplified `reply_classification`
   - **Impact**: Automated vs human reply counts may differ

### ⚠️ **POTENTIAL ISSUES**

1. **Meeting Attribution**
   - **Old**: Direct join to meeting data
   - **New**: Pre-aggregated in enriched view
   - **Impact**: Meeting metrics may differ

2. **NULL Handling**
   - **Old**: `IS NULL OR = ''`
   - **New**: `= 'unknown' OR IS NULL OR = ''`
   - **Impact**: Unknown email type counts may differ

3. **Grouping Dimensions**
   - **Old**: Groups by 5 dimensions
   - **New**: Groups by 15+ dimensions
   - **Impact**: More granular data may cause different totals

---

## Recommendation

The new flow needs to be restructured to match the old flow's logic exactly:

1. Use individual flags (0 or 1) per email record instead of pre-aggregation
2. Implement proper reply attribution using lead sequence flags
3. Use the same prospect email type source
4. Use the same reply classification system
5. Match the grouping dimensions exactly

This will ensure metric consistency while providing the new 9D analytical capabilities.

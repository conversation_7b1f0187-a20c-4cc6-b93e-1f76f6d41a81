DROP MATERIALIZED VIEW IF EXISTS public.seller_group_prospects_with_email_classification;
CREATE MATERIALIZED VIEW public.seller_group_prospects_with_email_classification AS
SELECT
  p.prospect_id,
  p.person_name,
  p.email,
  p.email_status,
  p.source,
  p.sources,
  p.person_linkedin,
  p.seller_id,
  p."emailProvider",
  p."mxRecords",

  /* ---------- e-mail classification ---------- */
  CASE
      /* Personal webmail domains (enhanced list) */
      WHEN lower(split_part(p.email, '@', 2)) IN (
          'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 'aol.com', 'icloud.com',
          '163.com', 'qq.com', '126.com', '139.com', 'sina.com', 'sohu.com',
          'mail.ru', 'yandex.ru', 'gmx.com', 'web.de', 'live.com', 'msn.com',
          'yahoo.co.uk', 'yahoo.ca', 'yahoo.com.au', 'googlemail.com'
      ) THEN 'personal'
      
      /* Role-based/Generic email addresses (comprehensive list) */
      WHEN lower(split_part(p.email, '@', 1)) IN (
          'info', 'support', 'help', 'service', 'customerservice', 'contact', 'hello',
          'care', 'customercare', 'customersupport', 'custserv', 'helpdesk', 'cs',
          'customer', 'customer.service', 'customer-service', 'customer_service',
          'customerservices', 'customer.services', 'customerserivce', 'customers',
          'customerexperience', 'customer.care', 'csr', 'wecare',
          'sales', 'marketing', 'sale', 'sales01', 'sales1', 'sales2', 'onlinesales',
          'corporatesales', 'b2b', 'business', 'wholesale', 'partner', 'partners',
          'partnership', 'partnerships', 'affiliate', 'affiliates', 'reseller',
          'dealer', 'distribution', 'export', 'purchasing',
          'admin', 'office', 'management', 'operations', 'accounts', 'accounting',
          'billing', 'bill', 'orders', 'order', 'shipping', 'returns', 'return',
          'rma', 'repairs', 'repair', 'warranty', 'complaints',
          'hr', 'humanresources', 'careers', 'jobs', 'recruitment', 'recruiting',
          'career', 'legal', 'privacy', 'dpo', 'gdpr', 'compliance', 'copyright',
          'dmca', 'privacypolicy', 'dataprotection', 'dataprivacy', 'policy',
          'privacyofficer', 'tech', 'technical', 'techsupport', 'webmaster', 'web',
          'website', 'it', 'postmaster', 'press', 'pr', 'media', 'social', 'socialmedia',
          'marketing', 'events', 'community', 'newsletter', 'news', 'shop', 'store',
          'ecommerce', 'shopify', 'weborders', 'online', 'enquiries', 'inquiries',
          'inquiry', 'questions', 'ask', 'feedback', 'team', 'official', 'email',
          'contactus', 'contacto', 'unsubscribe', 'noreply', 'no-reply', 'donotreply',
          'ceo', 'ambassador', 'ambassadors', 'influencer', 'influencers', 'education',
          'schools', 'membership', 'rewards', 'licensing', 'accessibility', 'international',
          'global', 'usa', 'uk', 'us'
      ) THEN 'role'
      
      /* Work emails - Institutional/Government domains + corporate domains */
      WHEN p.email ~* '\.(edu|gov|org|mil)$' THEN 'work'
      WHEN p.email ~* '@.*\.(com|net|io|co|biz)$' 
           AND lower(split_part(p.email, '@', 2)) NOT IN (
               'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 'aol.com', 'icloud.com',
               '163.com', 'qq.com', '126.com', '139.com', 'sina.com', 'sohu.com',
               'mail.ru', 'yandex.ru', 'gmx.com', 'web.de', 'live.com', 'msn.com',
               'yahoo.co.uk', 'yahoo.ca', 'yahoo.com.au', 'googlemail.com'
           ) THEN 'work'
      
      /* Unknown - NULL, empty, or unrecognized patterns */
      ELSE 'unknown'
  END AS email_type

FROM
  public."AmazonProspect" AS p;

-- ===================================================================
-- LEAD ANALYTICS CUBE - UPDATED FOR DOUBLE COUNTING FIX
-- ===================================================================
-- This materialized view aggregates lead primary flow data into a 
-- multi-dimensional analytics cube for business intelligence.
-- 
-- RECENT UPDATES:
-- - Fixed double counting issue in email sent metrics
-- - Added email_unknown_sent metrics for sequence 4+ emails
-- - Added combined reply metrics (total_replies, total_automated_replies, total_error_replies)
-- - Added performance metrics (reply rates, booking rates)
-- - Ensured proper aggregation without double counting
-- ===================================================================

DROP MATERIALIZED VIEW IF EXISTS public.mv_lead_analytics_cube CASCADE;
CREATE MATERIALIZED VIEW public.mv_lead_analytics_cube AS
SELECT
  -- DIMENSIONS
  week_start_date,
  prospect_email_provider,
  prospect_email_type,
  dominant_search_priority,
  prospect_parent_provider,
  campaign_name,
  jeff_campaign_code as funnel_code,
  inbox_provider_name,
  inbox_domain_name,
  inbox_parent_provider,
  tag_names,
  client_name,
  
  -- EMAIL SENT METRICS (Total)
  SUM(email_1_sent) as email_1_sent,
  SUM(email_2_sent) as email_2_sent,
  SUM(email_3_sent) as email_3_sent,
  SUM(email_unknown_sent_personal + email_unknown_sent_role + email_unknown_sent_work + email_unknown_sent_unknown) as email_unknown_sent,
  
  -- EMAIL SENT METRICS BY PROSPECT TYPE
  -- Email 1 by type
  SUM(email_1_sent_personal) as email_1_sent_personal,
  SUM(email_1_sent_role) as email_1_sent_role,
  SUM(email_1_sent_work) as email_1_sent_work,
  SUM(email_1_sent_unknown) as email_1_sent_unknown,
  
  -- Email 2 by type
  SUM(email_2_sent_personal) as email_2_sent_personal,
  SUM(email_2_sent_role) as email_2_sent_role,
  SUM(email_2_sent_work) as email_2_sent_work,
  SUM(email_2_sent_unknown) as email_2_sent_unknown,
  
  -- Email 3 by type
  SUM(email_3_sent_personal) as email_3_sent_personal,
  SUM(email_3_sent_role) as email_3_sent_role,
  SUM(email_3_sent_work) as email_3_sent_work,
  SUM(email_3_sent_unknown) as email_3_sent_unknown,

  -- Email unknown by type
  SUM(email_unknown_sent_personal) as email_unknown_sent_personal,
  SUM(email_unknown_sent_role) as email_unknown_sent_role,
  SUM(email_unknown_sent_work) as email_unknown_sent_work,
  SUM(email_unknown_sent_unknown) as email_unknown_sent_unknown,
  
  -- REPLY METRICS WITH PROPER ATTRIBUTION
  -- Replies after email 1
  SUM(replies_after_email_1) as replies_after_email_1,
  SUM(automated_replies_after_email_1) as automated_replies_after_email_1,
  SUM(error_replies_after_email_1) as error_replies_after_email_1,
  
  -- Replies after email 2
  SUM(replies_after_email_2) as replies_after_email_2,
  SUM(automated_replies_after_email_2) as automated_replies_after_email_2,
  SUM(error_replies_after_email_2) as error_replies_after_email_2,
  
  -- Replies after email 3
  SUM(replies_after_email_3) as replies_after_email_3,
  SUM(automated_replies_after_email_3) as automated_replies_after_email_3,
  SUM(error_replies_after_email_3) as error_replies_after_email_3,
  
  -- Replies after email unknown
  SUM(replies_after_email_unknown) as replies_after_email_unknown,
  SUM(automated_replies_after_email_unknown) as automated_replies_after_email_unknown,
  SUM(error_replies_after_email_unknown) as error_replies_after_email_unknown,
  
  -- COMBINED REPLY METRICS (Total replies by type)
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3 + replies_after_email_unknown) as total_replies,
  SUM(automated_replies_after_email_1 + automated_replies_after_email_2 + automated_replies_after_email_3 + automated_replies_after_email_unknown) as total_automated_replies,
  SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3 + error_replies_after_email_unknown) as total_error_replies,
  
  -- MEETING METRICS (direct aggregation - no duplication)
  SUM(COALESCE(meeting_slots_sent_after_1, 0)) as meeting_slots_sent_after_1,
  SUM(COALESCE(meeting_slots_sent_after_2, 0)) as meeting_slots_sent_after_2,
  SUM(COALESCE(meeting_slots_sent_after_3, 0)) as meeting_slots_sent_after_3,
  SUM(COALESCE(meeting_slots_sent_unknown, 0)) as meeting_slots_sent_unknown,
  SUM(COALESCE(meetings_booked, 0)) as meetings_booked,
  
  -- CALCULATED TOTAL METRICS
  SUM(email_1_sent + email_2_sent + email_3_sent + 
      email_unknown_sent_personal + email_unknown_sent_role + email_unknown_sent_work + email_unknown_sent_unknown) as total_emails_sent,
  SUM(COALESCE(meeting_slots_sent_after_1, 0) + COALESCE(meeting_slots_sent_after_2, 0) + 
      COALESCE(meeting_slots_sent_after_3, 0) + COALESCE(meeting_slots_sent_unknown, 0)) as total_meeting_slots_sent,
  
  -- PERFORMANCE METRICS (Calculated rates)
  CASE WHEN SUM(email_1_sent) > 0 
    THEN ROUND(100.0 * SUM(replies_after_email_1) / SUM(email_1_sent), 2) 
    ELSE 0 END as email_1_reply_rate,
  CASE WHEN SUM(email_2_sent) > 0 
    THEN ROUND(100.0 * SUM(replies_after_email_2) / SUM(email_2_sent), 2) 
    ELSE 0 END as email_2_reply_rate,
  CASE WHEN SUM(email_3_sent) > 0 
    THEN ROUND(100.0 * SUM(replies_after_email_3) / SUM(email_3_sent), 2) 
    ELSE 0 END as email_3_reply_rate,
  CASE WHEN SUM(email_1_sent + email_2_sent + email_3_sent + 
                email_unknown_sent_personal + email_unknown_sent_role + email_unknown_sent_work + email_unknown_sent_unknown) > 0 
    THEN ROUND(100.0 * SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3 + replies_after_email_unknown) / 
                SUM(email_1_sent + email_2_sent + email_3_sent + 
                    email_unknown_sent_personal + email_unknown_sent_role + email_unknown_sent_work + email_unknown_sent_unknown), 2) 
    ELSE 0 END as overall_reply_rate,
  CASE WHEN SUM(COALESCE(meeting_slots_sent_after_1, 0) + COALESCE(meeting_slots_sent_after_2, 0) + 
                COALESCE(meeting_slots_sent_after_3, 0) + COALESCE(meeting_slots_sent_unknown, 0)) > 0 
    THEN ROUND(100.0 * SUM(COALESCE(meetings_booked, 0)) / 
                SUM(COALESCE(meeting_slots_sent_after_1, 0) + COALESCE(meeting_slots_sent_after_2, 0) + 
                    COALESCE(meeting_slots_sent_after_3, 0) + COALESCE(meeting_slots_sent_unknown, 0)), 2) 
    ELSE 0 END as booking_rate
  

FROM public.mv_lead_primary_flow_data
GROUP BY 
  week_start_date,
  prospect_email_provider,
  prospect_email_type,
  dominant_search_priority,
  prospect_parent_provider,
  campaign_name,
  jeff_campaign_code,
  inbox_provider_name,
  inbox_domain_name,
  inbox_parent_provider,
  tag_names,
  client_name

-- Order by key dimensions for better performance
ORDER BY week_start_date, client_name, jeff_campaign_code, inbox_provider_name;

-- Performance indexes for optimal query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_week_start_date ON public.mv_lead_analytics_cube(week_start_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_client ON public.mv_lead_analytics_cube(client_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_funnel ON public.mv_lead_analytics_cube(funnel_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_inbox_provider ON public.mv_lead_analytics_cube(inbox_provider_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_prospect_provider ON public.mv_lead_analytics_cube(prospect_email_provider);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_prospect_email_type ON public.mv_lead_analytics_cube(prospect_email_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_search_priority ON public.mv_lead_analytics_cube(dominant_search_priority);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_date_client ON public.mv_lead_analytics_cube(week_start_date, client_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_date_funnel ON public.mv_lead_analytics_cube(week_start_date, funnel_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_week_client ON public.mv_lead_analytics_cube(week_start_date, client_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_week_funnel ON public.mv_lead_analytics_cube(week_start_date, funnel_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_client_funnel ON public.mv_lead_analytics_cube(client_name, funnel_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_provider_status ON public.mv_lead_analytics_cube(inbox_provider_name, prospect_email_provider);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_analytics_cube_email_type_provider ON public.mv_lead_analytics_cube(prospect_email_type, prospect_email_provider);

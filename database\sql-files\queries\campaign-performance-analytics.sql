SELECT 
  week_start_date,
  campaign_name,
  funnel_code,
  client_name,
  inbox_provider_name,
  tag_name,
  search_priority,
  prospect_email_provider,
  inbox_parent_provider,
  prospect_parent_provider,
  inbox_domain_name,
  
  -- CAMPAIGN REACH
  SUM(unique_leads_contacted) as unique_leads_contacted,
  SUM(total_emails_sent) as total_emails_sent,
  
  -- EMAIL TYPE DISTRIBUTION
  SUM(email_1_sent_work) as email_1_sent_work,
  SUM(email_1_sent_personal) as email_1_sent_personal,
  SUM(email_1_sent_role) as email_1_sent_role,
  SUM(email_1_sent_unknown) as email_1_sent_unknown,
  
  -- RESPONSE ANALYSIS
  SUM(replies_after_email_1 + replies_after_email_2 + replies_after_email_3) as total_human_replies,
  SUM(automated_replies_after_email_1 + automated_replies_after_email_2 + automated_replies_after_email_3) as total_auto_replies,
  SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3) as total_error_replies,
  
  -- SEQUENCE PERFORMANCE
  AVG(email_1_reply_rate) as avg_email_1_reply_rate,
  AVG(email_2_reply_rate) as avg_email_2_reply_rate,  
  AVG(email_3_reply_rate) as avg_email_3_reply_rate,
  
  -- MEETING OUTCOMES
  SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1,
  SUM(meeting_slots_sent_after_2) as meeting_slots_sent_after_2,
  SUM(meeting_slots_sent_after_3) as meeting_slots_sent_after_3,
  SUM(meeting_slots_sent_unknown) as meeting_slots_sent_unknown,
  SUM(meetings_booked) as meetings_booked,
  SUM(total_meeting_slots_sent) as total_meeting_slots_sent,
  AVG(booking_rate) as avg_booking_rate,
  
  -- CAMPAIGN EFFICIENCY
  AVG(overall_reply_rate) as overall_reply_rate,
  CASE 
    WHEN SUM(total_emails_sent) > 0 
    THEN ROUND(SUM(meetings_booked)::decimal / SUM(total_emails_sent) * 1000, 2)
    ELSE 0 
  END as meetings_per_1000_emails

FROM public.mv_analytics_cube_9d
GROUP BY 
  week_start_date, campaign_name, funnel_code, client_name, inbox_provider_name, tag_name,
  search_priority, prospect_email_provider, inbox_parent_provider, prospect_parent_provider, inbox_domain_name
ORDER BY 
  week_start_date DESC, total_emails_sent DESC
#!/bin/bash
# refresh_materialized_views.sh
#
# Refreshes PostgreSQL materialized views.
#
# USAGE:
#   For cron (non-interactive): ./refresh_materialized_views.sh all
#   For manual use (interactive): ./refresh_materialized_views.sh

# --- Configuration ---
# These are expected to be set in your environment (.bashrc, .profile, etc.)
# PGHOST="localhost"
# PGUSER="your_user"
# PGDATABASE="your_db"
# PGPORT=5432
# export PGPASSWORD="your_password"

# The directory containing the SQL files to be run.
SQL_DIR="$(dirname "$0")/database/sql-files/m-view/models"

# --- Functions ---

# Executes a single SQL file against the database.
# @param $1: The full path to the SQL file.
run_sql_file() {
  local sqlfile="$1"
  # Check if the file parameter was passed
  if [ -z "$sqlfile" ]; then
    echo "❌ ERROR: No SQL file specified."
    return 1
  fi
  
  echo "-> Running $(basename "$sqlfile")..."
  psql -v ON_ERROR_STOP=1 -h "$PGHOST" -U "$PGUSER" -d "$PGDATABASE" -p "$PGPORT" -f "$sqlfile"
  
  if [ $? -ne 0 ]; then
    echo "❌ ERROR: Failed to execute $sqlfile."
    return 1
  fi
  return 0
}

# Runs all .sql files found in the SQL_DIR in dependency order.
run_all_files() {
  echo "🚀 Starting execution of materialized view scripts in dependency order..."
  
  # Define the correct execution order based on dependencies
  declare -a ordered_files=(
    "$SQL_DIR/119-sellers-with-search-priority.sql"
    "$SQL_DIR/mv_email_with_thread_start.sql"
    "$SQL_DIR/338-sellers-with-only-search-priority.sql"
    "$SQL_DIR/274-seller-group-sellers-with-search-priority-expanded.sql"
    "$SQL_DIR/270-seller-group-prospects-with-email-classification.sql"
    "$SQL_DIR/271-seller-group-sellers-with-sp-classifications-prospects-w-emails.sql"
    "$SQL_DIR/mv_lead_primary_flow_data.sql"
    "$SQL_DIR/mv_lead_analytics_cube.sql"
    "$SQL_DIR/277-smartlead-emails-with-custom-reply-status.sql"
    "$SQL_DIR/278-smartlead-analytics.sql"
    "$SQL_DIR/289-smartlead-analytics-with-tags.sql"
    "$SQL_DIR/313-smartlead-analytics-by-provider-client.sql"
  )
  
  # Execute files in the correct order
  for sqlfile in "${ordered_files[@]}"; do
    # Check if the file exists
    if [ ! -f "$sqlfile" ]; then
      echo "⚠️ WARNING: File $sqlfile not found. Skipping."
      continue
    fi
    
    run_sql_file "$sqlfile"
    # If any script fails, exit the entire process.
    if [ $? -ne 0 ]; then
      echo "Stopping execution due to an error."
      exit 1
    fi
  done
  
  echo "✅ All materialized view scripts executed successfully in dependency order."
}


# --- Main Logic ---

# If the first argument is "all", run in non-interactive mode for cron.
if [[ "$1" == "all" ]]; then
  run_all_files
  exit 0
fi

# If no argument is provided, run in interactive mode.
echo "Interactive Mode: Select a script to run."

# Get an array of all .sql files.
files=("$SQL_DIR"/*.sql)

# Check if any .sql files were found.
if [[ ! -f "${files[0]}" ]]; then
  echo "⚠️ No .sql files found in $SQL_DIR. Exiting."
  exit 1
fi

# Use the 'select' command to create a menu.
PS3="Your choice: " # Sets the prompt for the select menu.
options=()
for file in "${files[@]}"; do
  options+=("$(basename "$file")")
done
options+=("All" "Quit")

select choice in "${options[@]}"; do
  case $choice in
    "All")
      run_all_files
      break
      ;;
    "Quit")
      echo "Exiting."
      break
      ;;
    *)
      # Check if the choice is valid (i.e., one of the file names).
      if [[ -n "$choice" ]]; then
        run_sql_file "$SQL_DIR/$choice"
        break
      else
        echo "Invalid option. Please try again."
      fi
      ;;
  esac
done

exit 0
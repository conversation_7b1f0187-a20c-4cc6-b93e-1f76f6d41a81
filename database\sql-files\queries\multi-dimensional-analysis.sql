SELECT 
  week_start_date,
  
  -- Cross-dimensional analysis with all dimensions
  client_name,
  funnel_code,
  tag_name,
  search_priority,
  inbox_provider_name,
  prospect_email_provider,
  campaign_name,
  inbox_parent_provider,
  prospect_parent_provider,
  inbox_domain_name,
  
  -- AGGREGATED METRICS
  SUM(total_emails_sent) as total_emails_sent,
  SUM(total_replies) as total_replies,
  AVG(overall_reply_rate) as avg_reply_rate,
  
  -- MEETING METRICS
  SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1,
  SUM(meeting_slots_sent_after_2) as meeting_slots_sent_after_2,
  <PERSON>UM(meeting_slots_sent_after_3) as meeting_slots_sent_after_3,
  <PERSON><PERSON>(meeting_slots_sent_unknown) as meeting_slots_sent_unknown,
  SUM(meetings_booked) as meetings_booked,
  SUM(total_meeting_slots_sent) as total_meeting_slots_sent,
  AVG(booking_rate) as avg_booking_rate,
  
  -- EFFICIENCY MEASURES
  SUM(unique_leads_contacted) as unique_leads_contacted,
  CASE 
    WHEN SUM(unique_leads_contacted) > 0 
    THEN ROUND(SUM(total_replies)::decimal / SUM(unique_leads_contacted), 2)
    ELSE 0 
  END as replies_per_lead,
  
  CASE 
    WHEN SUM(unique_leads_contacted) > 0 
    THEN ROUND(SUM(meetings_booked)::decimal / SUM(unique_leads_contacted) * 100, 2)
    ELSE 0 
  END as meeting_conversion_rate

FROM public.mv_analytics_cube_9d
GROUP BY 
  week_start_date, client_name, funnel_code, tag_name, search_priority,
  inbox_provider_name, prospect_email_provider, campaign_name, inbox_parent_provider, prospect_parent_provider, inbox_domain_name
ORDER BY 
  week_start_date DESC, total_emails_sent DESC
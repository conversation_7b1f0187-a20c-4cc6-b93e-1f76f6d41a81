DROP MATERIALIZED VIEW IF EXISTS public.mv_email_with_thread_start CASCADE;


CREATE MATERIALIZED VIEW public.mv_email_with_thread_start AS
WITH
  email_with_thread_start AS (
    SELECT
      id,
      "leadId",
      subject,
      body,
      type,
      "toEmailID",
      "fromEmailID",
      "time",
      "messageId",
      "campaingId",
      email_seq_number,
      open_count,
      -- Thread start email ID is the better dimension for thread analysis
      first_value("fromEmailID") OVER (
        PARTITION BY
          "leadId"
        ORDER BY
          CASE
            WHEN (
              "fromEmailID" = '<EMAIL>'::TEXT
            ) THEN 2
            WHEN ("fromEmailID" ~~ '%.onmicrosoft.com'::TEXT) THEN 2
            ELSE 1
          END,
          "time",
          CASE
            WHEN (type = 'SENT'::TEXT) THEN 1
            WHEN (type = 'FORWARD'::TEXT) THEN 2
            WHEN (type = 'REPLY'::TEXT) THEN 3
            ELSE 4
          END ROWS BETWEEN UNBOUNDED PRECEDING
          AND UNBOUNDED FOLLOWING
      ) AS "threadStartEmailId",
      -- Thread to email ID is the better dimension for thread analysis
      first_value("toEmailID") OVER (
        PARTITION BY
          "leadId"
        ORDER BY
          CASE
            WHEN (
              "fromEmailID" = '<EMAIL>'::TEXT
            ) THEN 2
            WHEN ("fromEmailID" ~~ '%.onmicrosoft.com'::TEXT) THEN 2
            ELSE 1
          END,
          "time",
          CASE
            WHEN (type = 'SENT'::TEXT) THEN 1
            WHEN (type = 'FORWARD'::TEXT) THEN 2
            WHEN (type = 'REPLY'::TEXT) THEN 3
            ELSE 4
          END ROWS BETWEEN UNBOUNDED PRECEDING
          AND UNBOUNDED FOLLOWING
      ) AS "threadToEmailId"
    FROM
      "Email"
  ),
  email_with_classification AS (
    SELECT
      *,
      CASE
        /* Personal webmail domains (enhanced list) */
        WHEN lower(split_part("threadToEmailId", '@', 2)) IN (
            -- Major international personal email providers
            'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 'aol.com', 'icloud.com',
            -- Chinese providers
            '163.com', 'qq.com', '126.com', '139.com', 'sina.com', 'sohu.com',
            -- Other international providers
            'mail.ru', 'yandex.ru', 'gmx.com', 'web.de', 'live.com', 'msn.com',
            'yahoo.co.uk', 'yahoo.ca', 'yahoo.com.au', 'googlemail.com'
        ) THEN 'personal'

        /* Role-based/Generic email addresses (comprehensive list) */
        WHEN lower(split_part("threadToEmailId", '@', 1)) IN (
            -- Customer service & support
            'info', 'support', 'help', 'service', 'customerservice', 'contact', 'hello',
            'care', 'customercare', 'customersupport', 'custserv', 'helpdesk', 'cs',
            'customer', 'customer.service', 'customer-service', 'customer_service',
            'customerservices', 'customer.services', 'customerserivce', 'customers',
            'customerexperience', 'customer.care', 'csr', 'wecare',
            
            -- Sales & marketing
            'sales', 'marketing', 'sale', 'sales01', 'sales1', 'sales2', 'onlinesales',
            'corporatesales', 'b2b', 'business', 'wholesale', 'partner', 'partners',
            'partnership', 'partnerships', 'affiliate', 'affiliates', 'reseller',
            'dealer', 'distribution', 'export', 'purchasing',
            
            -- Administrative
            'admin', 'office', 'management', 'operations', 'accounts', 'accounting',
            'billing', 'bill', 'orders', 'order', 'shipping', 'returns', 'return',
            'rma', 'repairs', 'repair', 'warranty', 'complaints',
            
            -- HR & careers
            'hr', 'humanresources', 'careers', 'jobs', 'recruitment', 'recruiting',
            'career',
            
            -- Legal & compliance
            'legal', 'privacy', 'dpo', 'gdpr', 'compliance', 'copyright', 'dmca',
            'privacypolicy', 'dataprotection', 'dataprivacy', 'policy', 'privacyofficer',
            
            -- Technical
            'tech', 'technical', 'techsupport', 'webmaster', 'web', 'website', 'it',
            'postmaster',
            
            -- Marketing & communications
            'press', 'pr', 'media', 'social', 'socialmedia', 'marketing', 'events',
            'community', 'newsletter', 'news',
            
            -- E-commerce specific
            'shop', 'store', 'ecommerce', 'shopify', 'weborders', 'online',
            'enquiries', 'inquiries', 'inquiry', 'questions', 'ask', 'feedback',
            
            -- Brand/company specific
            'team', 'official', 'email', 'contactus', 'contacto', 'unsubscribe',
            'noreply', 'no-reply', 'donotreply',
            
            -- Other common roles
            'ceo', 'ambassador', 'ambassadors', 'influencer', 'influencers',
            'education', 'schools', 'membership', 'rewards', 'licensing',
            'accessibility', 'international', 'global', 'usa', 'uk', 'us'
        ) THEN 'role'

        /* Work emails - Institutional/Government domains + corporate domains */
        WHEN "threadToEmailId" ~* '\.(edu|gov|org|mil)$' THEN 'work'
        WHEN "threadToEmailId" ~* '@.*\.(com|net|io|co|biz)$' 
               AND lower(split_part("threadToEmailId", '@', 2)) NOT IN (
                  'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 'aol.com', 'icloud.com',
                  '163.com', 'qq.com', '126.com', '139.com', 'sina.com', 'sohu.com',
                  'mail.ru', 'yandex.ru', 'gmx.com', 'web.de', 'live.com', 'msn.com',
                  'yahoo.co.uk', 'yahoo.ca', 'yahoo.com.au', 'googlemail.com'
              ) THEN 'work'

        /* Unknown - NULL, empty, or unrecognized patterns */
        ELSE 'unknown'  
      END AS email_category,
      CASE
        WHEN type != 'REPLY' THEN NULL
        WHEN body ~* '(out of office|automatic reply|auto.?reply)' THEN 'AUTOMATED'
        WHEN body ~* '(undeliverable|delivery failure|bounce|failed)' THEN 'ERROR'
        WHEN body ~* '(unsubscribe|remove me|stop email)' THEN 'UNSUBSCRIBE'
        ELSE 'HUMAN'
      END AS reply_classification,
      CASE
        WHEN "type" IN ('FORWARD', 'SENT') THEN "type"
        WHEN "type" = 'REPLY' THEN CASE
          WHEN LOWER("messageId") LIKE '%zendesk%'
          OR LOWER("messageId") LIKE '%freshdesk%'
          OR LOWER("messageId") LIKE '%helpscout%'
          OR LOWER("messageId") LIKE '%hubspot%'
          OR LOWER("messageId") LIKE '%intercom%'
          OR LOWER("body") LIKE '%is an automated message%'
          OR LOWER("body") LIKE '%important to us%'
          OR LOWER("body") LIKE '%will get back to you as quickly as possible%'
          OR LOWER("body") LIKE '%customer service%'
          OR LOWER("body") LIKE '%auto message%'
          OR LOWER("body") LIKE '%customer care%'
          OR LOWER("body") LIKE '%will get back to you within%'
          OR LOWER("body") LIKE '%auto-reply%'
          OR LOWER("body") LIKE '%for your patience%'
          OR LOWER("body") LIKE '%thank you for your request%'
          OR LOWER("body") LIKE '%business hours are%'
          OR LOWER("body") LIKE '%received your request.%'
          OR LOWER("body") LIKE '%hear back from us within%'
          OR LOWER("body") LIKE '%thank you for contacting%'
          OR LOWER("body") LIKE '%thank you for reaching%'
          OR LOWER("body") LIKE '%support experience%'
          OR LOWER("body") LIKE '%support team%'
          OR LOWER("body") LIKE '%**is an automated message**%' THEN 'REPLY_CS_AUTOMATED'
          WHEN LOWER("messageId") LIKE '%mx.googlcom%' THEN 'ERROR_REPLY'
          WHEN (
            LOWER("messageId") LIKE '%prod.outlook.com%'
            OR LOWER("messageId") LIKE '%exchangelabs.com%'
          )
          AND (
            LOWER("body") LIKE '%fail%'
            OR LOWER("body") LIKE '%failure%'
            OR LOWER("body") LIKE '%error%'
            OR LOWER("body") LIKE '%action required recipient unknown%'
            OR LOWER("body") LIKE '%your message to%'
            OR LOWER("body") LIKE '%rejected%'
          ) THEN 'ERROR_REPLY'
          WHEN LOWER("body") LIKE '%automatically by mail delivery software%'
          OR LOWER("body") LIKE '%delivery has failed%'
          OR LOWER("body") LIKE '%created automatically by mail delivery software%'
          OR LOWER("body") LIKE '%your message could not be delivered%'
          OR LOWER("body") LIKE '%message could not be delivered%'
          OR LOWER("body") LIKE '%recipient address rejected%'
          OR (
            LOWER("body") LIKE '%mail system%'
            AND LOWER("body") LIKE '%rejected%'
          ) THEN 'ERROR_REPLY'
          ELSE 'REPLY'
        END
        ELSE NULL
      END AS jeff_email_status
    FROM
      email_with_thread_start
  )
SELECT DISTINCT ON (
    "threadToEmailId",
    "threadStartEmailId", 
    DATE("time"),
    type,
    email_seq_number
  )
  *
FROM
  email_with_classification
ORDER BY 
  "threadToEmailId",
  "threadStartEmailId", 
  DATE("time"),
  type,
  email_seq_number,
  id;


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_leadid ON public.mv_email_with_thread_start ("leadId");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_type ON public.mv_email_with_thread_start (type);


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_toemailid ON public.mv_email_with_thread_start ("toEmailID");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_fromemailid ON public.mv_email_with_thread_start ("fromEmailID");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_time ON public.mv_email_with_thread_start ("time");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_messageid ON public.mv_email_with_thread_start ("messageId");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_campaingid ON public.mv_email_with_thread_start ("campaingId");


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_email_seq_number ON public.mv_email_with_thread_start (email_seq_number);


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_open_count ON public.mv_email_with_thread_start (open_count);

CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_email_category ON public.mv_email_with_thread_start (email_category);


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_reply_classification ON public.mv_email_with_thread_start (reply_classification);


CREATE INDEX CONCURRENTLY idx_mv_email_with_thread_start_jeff_email_status ON public.mv_email_with_thread_start (jeff_email_status);

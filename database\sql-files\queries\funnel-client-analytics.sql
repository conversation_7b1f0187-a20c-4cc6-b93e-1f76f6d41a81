-- METABASE QUERY: Funnel Client Analytics with Lead Analytics Cube
-- Purpose: Enhanced version using mv_lead_analytics_cube with all dimensions

SELECT 
  week_start_date,
  client_name,
  funnel_code as campaign_code,
  funnel_code,
  
  -- EMAIL 1 BREAKDOWN BY TYPE
  SUM(email_1_sent) as email_1_sent,
  SUM(email_1_sent_work) as email_1_sent_work,
  <PERSON>UM(email_1_sent_personal) as email_1_sent_personal, 
  SUM(email_1_sent_role) as email_1_sent_role,
  SUM(email_1_sent_unknown) as email_1_sent_unknown,
  
  -- EMAIL 1 RESPONSES
  SUM(replies_after_email_1) as replies_after_email_1,
  <PERSON><PERSON>(automated_replies_after_email_1) as automated_replies_after_email_1,
  <PERSON><PERSON>(error_replies_after_email_1) as error_replies_after_email_1,
  <PERSON><PERSON>(replies_after_email_1 + automated_replies_after_email_1) as total_replies_after_email_1,
  
  -- EMAIL 2 BREAKDOWN BY TYPE
  SUM(email_2_sent) as email_2_sent,
  <PERSON><PERSON>(email_2_sent_work) as email_2_sent_work,
  <PERSON><PERSON>(email_2_sent_personal) as email_2_sent_personal,
  <PERSON><PERSON>(email_2_sent_role) as email_2_sent_role,
  SUM(email_2_sent_unknown) as email_2_sent_unknown,
  
  -- EMAIL 2 RESPONSES  
  SUM(replies_after_email_2) as replies_after_email_2,
  SUM(automated_replies_after_email_2) as automated_replies_after_email_2,
  SUM(error_replies_after_email_2) as error_replies_after_email_2,
  SUM(replies_after_email_2 + automated_replies_after_email_2) as total_replies_after_email_2,
  
  -- EMAIL 3 BREAKDOWN BY TYPE
  SUM(email_3_sent) as email_3_sent,
  SUM(email_3_sent_work) as email_3_sent_work,
  SUM(email_3_sent_personal) as email_3_sent_personal,
  SUM(email_3_sent_role) as email_3_sent_role,
  SUM(email_3_sent_unknown) as email_3_sent_unknown,
  
  -- EMAIL 3 RESPONSES
  SUM(replies_after_email_3) as replies_after_email_3,
  SUM(automated_replies_after_email_3) as automated_replies_after_email_3,
  SUM(error_replies_after_email_3) as error_replies_after_email_3,
  SUM(replies_after_email_3 + automated_replies_after_email_3) as total_replies_after_email_3,
  
  -- MEETING ATTRIBUTION
  SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1,
  SUM(meeting_slots_sent_after_2) as meeting_slots_sent_after_2,
  SUM(meeting_slots_sent_after_3) as meeting_slots_sent_after_3,
  SUM(meeting_slots_sent_unknown) as meeting_slots_sent_unknown,
  SUM(meetings_booked) as meetings_booked,
  SUM(total_meeting_slots_sent) as total_meeting_slots_sent,
  
  -- PERFORMANCE METRICS
  AVG(email_1_reply_rate) as avg_email_1_reply_rate,
  AVG(email_2_reply_rate) as avg_email_2_reply_rate,
  AVG(email_3_reply_rate) as avg_email_3_reply_rate,
  AVG(overall_reply_rate) as overall_reply_rate,
  AVG(booking_rate) as avg_booking_rate,
  
  -- SUMMARY METRICS
  SUM(total_emails_sent) as total_emails_sent,
  SUM(total_replies) as total_replies,
  SUM(unique_leads_contacted) as unique_leads_contacted

FROM public.mv_lead_analytics_cube
GROUP BY 
  week_start_date, client_name, funnel_code, campaign_code
ORDER BY 
  week_start_date DESC, client_name, funnel_code
DROP MATERIALIZED VIEW IF EXISTS public.mv_lead_primary_flow_data CASCADE;
CREATE MATERIALIZED VIEW public.mv_lead_primary_flow_data AS
WITH 
-- Campaign parsing for Funnel and Campaign dimensions
campaign_parsing AS (
  SELECT
    c."campaignId",
    c."name" AS campaign_name,
    c."clientId",
    c."parentCampaignId" AS parent_campaign_id,
    CASE
      WHEN SUBSTRING(
        c."name"
        FROM
          '[0-9]{1,2}[A-Z]'
      ) IS NOT NULL THEN SUBSTRING(
        c."name"
        FROM
          '[0-9]{1,2}[A-Z]'
      )
      WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
      ELSE 'UNK'
    END AS jeff_campaign_code,
    -- Campaign series: number from campaign code
    COALESCE(
      substring(
        substring(
          c."name"
          FROM
            '([0-9]+[A-Z])'
        ) -- funnel_code
        FROM
          '([0-9]+)' -- number from funnel_code
      ),
      'UNK'
    ) AS campaign_series
  FROM
    public."Campaign" c
),
-- Ensure a single campaign mapping per (client, funnel code) to avoid 1:<PERSON> joins in meetings
campaign_parsing_unique AS (
  SELECT DISTINCT ON (cp."clientId", cp.jeff_campaign_code)
    cp."clientId",
    cp.jeff_campaign_code,
    cp.campaign_name,
    cp.campaign_series,
    cp."campaignId"
  FROM campaign_parsing cp
  ORDER BY cp."clientId", cp.jeff_campaign_code, cp."campaignId" DESC
),

-- Client information for Client Name dimension
client_info AS (
  SELECT 
    c."clientId",
    COALESCE(c."businessName", c."name") as client_name
  FROM public."Client" c
),

-- CRITICAL FIX: Use same prospect data source as old flow for exact metric matching
-- FIXED: Use DISTINCT ON to avoid duplicates from multiple prospect records
amazon_prospect_enrichment AS (
  SELECT DISTINCT ON (sp271.email)
    sp271.email as prospect_email,
    sp271."emailProvider" as prospect_email_provider,
    -- Classify prospect parent provider from emailProvider
    CASE 
      WHEN sp271."emailProvider" ~* '(gmail|google)' THEN 'Google'
      WHEN sp271."emailProvider" ~* '(outlook|hotmail|live|microsoft)' THEN 'Outlook'  
      WHEN sp271."emailProvider" ~* 'zoho' THEN 'Zoho'
      WHEN sp271."emailProvider" ~* 'yahoo' THEN 'Yahoo'
      WHEN sp271."emailProvider" ~* 'aol' THEN 'AOL'
      WHEN sp271."emailProvider" = '' OR sp271."emailProvider" IS NULL THEN 'Unknown'
      ELSE 'Others'
    END as prospect_parent_provider,
    -- Get search priority - same as old flow
    COALESCE(sp271."jeff_search_priority", 'UNK') as search_priority,
    COALESCE(sp271."dominant_search_priority", 'UNK') as dominant_search_priority,
    -- CRITICAL: Use pre-classified email type from same table as old flow
    sp271.email_type AS prospect_email_type

  FROM public."seller_group_sellers_with_sp_classifications_prospects_w_emails" sp271
  ORDER BY sp271.email, sp271.id DESC
),

-- Inbox provider information for Inbox Provider & Parent Provider dimensions & Tag dimension
-- FIXED: Use DISTINCT ON to avoid duplicates from multiple tag records
tag_enrichment AS (
  SELECT
    t."inboxId",
    ARRAY_AGG(t."tag") as tag_names
  FROM public2."InboxTagsFromReplit" t
  GROUP BY t."inboxId"
),
inbox_enrichment AS (
  SELECT
      i."inboxEmail",
      COALESCE(i."provider_name", 'UNK') as inbox_provider_name,
      COALESCE(i."domain", 'UNK') as inbox_domain_name,
      CASE 
        WHEN i."provider_name" ~* '(gmail|google)' THEN 'Google'
        WHEN i."provider_name" ~* '(outlook|hotmail|live|microsoft)' THEN 'Outlook'
        WHEN i."provider_name" ~* 'zoho' THEN 'Zoho'  
        WHEN i."provider_name" ~* 'yahoo' THEN 'Yahoo'
        WHEN i."provider_name" ~* 'aol' THEN 'AOL'
        WHEN i."provider_name" = '' OR i."provider_name" IS NULL THEN 'Unknown'
        ELSE 'Others'
      END as inbox_parent_provider,
      COALESCE(t.tag_names, ARRAY['no_tag']) as tag_names
    FROM public2."InboxesFromReplit" i
    LEFT JOIN tag_enrichment t ON i.id = t."inboxId"
),

-- Base data for primary lead flow with business metrics
email_classification AS (
    SELECT
      e."leadId",
      -- Actual Time Dimesion
      e."time",
      -- Campaing ID
      e."campaingId",
      -- Actual Email Sequence Number 1 2 3 ...
      e.email_seq_number,
      e.open_count,
      -- Actual Inbox emails after gardrail
      e."threadStartEmailId",
      -- Actual lead emails gardrail
      e."threadToEmailId",
      -- [ work/ personal / role type for the to email] 
      e.email_category,
      -- [ REPLY_CS_AUTOMATED / ERROR_REPLY / REPLY / FORWARD / SENT ]
      e.jeff_email_status,
      -- [ AUTOMATED / ERROR / UNSUBSCRIBE / HUMAN / Null (For REPLY) ]
      e.reply_classification,
      -- Get next email time for meeting attribution window
      LEAD(e."time") OVER (
        PARTITION BY e."leadId", e."campaingId" 
        ORDER BY e."time"
      ) as next_email_time,
      
      -- BUSINESS METRICS: Create individual flags per email record (like old flow)
      CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_1_sent,
      CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_2_sent,
      CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_3_sent,
      CASE WHEN (e.email_seq_number NOT IN ('1', '2', '3') OR e.email_seq_number IS NULL) AND e.type = 'SENT' THEN 1 ELSE 0 END AS email_unknown_sent,
      
      -- Create flags for email sequences sent by prospect type (like old flow)
      -- Email 1
      CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' AND e.email_category = 'personal' THEN 1 ELSE 0 END AS email_1_sent_personal,
      CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' AND e.email_category = 'role' THEN 1 ELSE 0 END AS email_1_sent_role,
      CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' AND e.email_category = 'work' THEN 1 ELSE 0 END AS email_1_sent_work,
      CASE WHEN e.email_seq_number = '1' AND e.type = 'SENT' AND (e.email_category IS NULL OR e.email_category = '' OR e.email_category = 'unknown') THEN 1 ELSE 0 END AS email_1_sent_unknown,
      
      -- Email 2
      CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' AND e.email_category = 'personal' THEN 1 ELSE 0 END AS email_2_sent_personal,
      CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' AND e.email_category = 'role' THEN 1 ELSE 0 END AS email_2_sent_role,
      CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' AND e.email_category = 'work' THEN 1 ELSE 0 END AS email_2_sent_work,
      CASE WHEN e.email_seq_number = '2' AND e.type = 'SENT' AND (e.email_category IS NULL OR e.email_category = '' OR e.email_category = 'unknown') THEN 1 ELSE 0 END AS email_2_sent_unknown,
      
      -- Email 3
      CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' AND e.email_category = 'personal' THEN 1 ELSE 0 END AS email_3_sent_personal,
      CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' AND e.email_category = 'role' THEN 1 ELSE 0 END AS email_3_sent_role,
      CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' AND e.email_category = 'work' THEN 1 ELSE 0 END AS email_3_sent_work,
      CASE WHEN e.email_seq_number = '3' AND e.type = 'SENT' AND (e.email_category IS NULL OR e.email_category = '' OR e.email_category = 'unknown') THEN 1 ELSE 0 END AS email_3_sent_unknown,

      -- Email Unknown
      CASE WHEN (e.email_seq_number NOT IN ('1', '2', '3') OR e.email_seq_number IS NULL) AND e.type = 'SENT' AND e.email_category = 'personal' THEN 1 ELSE 0 END AS email_unknown_sent_personal,
      CASE WHEN (e.email_seq_number NOT IN ('1', '2', '3') OR e.email_seq_number IS NULL) AND e.type = 'SENT' AND e.email_category = 'role' THEN 1 ELSE 0 END AS email_unknown_sent_role,
      CASE WHEN (e.email_seq_number NOT IN ('1', '2', '3') OR e.email_seq_number IS NULL) AND e.type = 'SENT' AND e.email_category = 'work' THEN 1 ELSE 0 END AS email_unknown_sent_work,
      CASE WHEN (e.email_seq_number NOT IN ('1', '2', '3') OR e.email_seq_number IS NULL) AND e.type = 'SENT' AND (e.email_category IS NULL OR e.email_category = '' OR e.email_category = 'unknown') THEN 1 ELSE 0 END AS email_unknown_sent_unknown,
      
      -- Create flags for replies (like old flow)
      CASE WHEN e.jeff_email_status = 'REPLY' THEN 1 ELSE 0 END AS is_reply,
      CASE WHEN e.jeff_email_status = 'REPLY_CS_AUTOMATED' THEN 1 ELSE 0 END AS is_auto_reply,
      CASE WHEN e.jeff_email_status = 'ERROR_REPLY' THEN 1 ELSE 0 END AS is_error_reply
    FROM
      public.mv_email_with_thread_start e
),

-- UNIFIED DATA: Combine Email and Meeting records with UNION ALL
unified_data AS (
  -- EMAIL RECORDS: Email metrics populated, meeting metrics = 0
  SELECT 
    ec."threadToEmailId" as prospect_email,
    ec."threadStartEmailId" as inbox_email,
    -- Use daily date for temporal analysis (use original timestamp)
    DATE(ec."time") as email_date,
    -- Week start date (Monday) for weekly analysis
    DATE_TRUNC('week', ec."time")::date as week_start_date,
    
    -- Prospect Info
    COALESCE(ape.prospect_email_provider, 'UNK') as prospect_email_provider,
    COALESCE(ape.dominant_search_priority, 'UNK') as dominant_search_priority,
    COALESCE(ape.prospect_email_type, 'UNK') as prospect_email_type,
    COALESCE(ape.prospect_parent_provider, 'UNK') as prospect_parent_provider,
    
    -- Campaign Info
    cp.campaign_series,
    cp.campaign_name,
    cp.jeff_campaign_code,
    
    -- Inbox Provider Name
    ie.inbox_provider_name,
    ie.inbox_domain_name,
    ie.inbox_parent_provider,
    ie.tag_names,
    
    -- Client Name
    ci.client_name,
    
    -- Email sequence number (critical for proper aggregation)
    ec.email_seq_number,

    -- EMAIL METRICS: Populated from email data
    ec.email_1_sent,
    ec.email_2_sent,
    ec.email_3_sent,
    
    -- Email Sent Counts by Prospect Type
    ec.email_1_sent_personal,
    ec.email_1_sent_role,
    ec.email_1_sent_work,
    ec.email_1_sent_unknown,
    
    ec.email_2_sent_personal,
    ec.email_2_sent_role,
    ec.email_2_sent_work,
    ec.email_2_sent_unknown,
    
    ec.email_3_sent_personal,
    ec.email_3_sent_role,
    ec.email_3_sent_work,
    ec.email_3_sent_unknown,
    
    ec.email_unknown_sent_personal,
    ec.email_unknown_sent_role,
    ec.email_unknown_sent_work,
    ec.email_unknown_sent_unknown,
    
    -- Reply Counts with Proper Attribution
    CASE WHEN ec.is_reply = 1 AND ec.email_seq_number = '1' THEN 1 ELSE 0 END AS manualreplies_after_email_1,
    CASE WHEN ec.is_auto_reply = 1 AND ec.email_seq_number = '1' THEN 1 ELSE 0 END AS automated_replies_after_email_1,
    CASE WHEN ec.is_error_reply = 1 AND ec.email_seq_number = '1' THEN 1 ELSE 0 END AS error_replies_after_email_1,

    CASE WHEN ec.is_reply = 1 AND ec.email_seq_number = '2' THEN 1 ELSE 0 END AS manual_replies_after_email_2,
    CASE WHEN ec.is_auto_reply = 1 AND ec.email_seq_number = '2' THEN 1 ELSE 0 END AS automated_replies_after_email_2,
    CASE WHEN ec.is_error_reply = 1 AND ec.email_seq_number = '2' THEN 1 ELSE 0 END AS error_replies_after_email_2,
    
    CASE WHEN ec.is_reply = 1 AND ec.email_seq_number = '3' THEN 1 ELSE 0 END AS manual_replies_after_email_3,
    CASE WHEN ec.is_auto_reply = 1 AND ec.email_seq_number = '3' THEN 1 ELSE 0 END AS automated_replies_after_email_3,
    CASE WHEN ec.is_error_reply = 1 AND ec.email_seq_number = '3' THEN 1 ELSE 0 END AS error_replies_after_email_3,

    CASE WHEN ec.is_reply = 1 AND (ec.email_seq_number IS NULL OR ec.email_seq_number NOT IN ('1', '2', '3')) THEN 1 ELSE 0 END AS manual_replies_after_email_unknown,
    CASE WHEN ec.is_auto_reply = 1 AND (ec.email_seq_number NOT IN ('1', '2', '3') OR ec.email_seq_number IS NULL) THEN 1 ELSE 0 END AS automated_replies_after_email_unknown,
    CASE WHEN ec.is_error_reply = 1 AND (ec.email_seq_number NOT IN ('1', '2', '3') OR ec.email_seq_number IS NULL) THEN 1 ELSE 0 END AS error_replies_after_email_unknown,
    
    -- Combined reply metrics (manual + auto + error)
    CASE WHEN (ec.is_reply = 1 OR ec.is_auto_reply = 1 OR ec.is_error_reply = 1) AND ec.email_seq_number = '1' THEN 1 ELSE 0 END AS replies_after_email_1,
    CASE WHEN (ec.is_reply = 1 OR ec.is_auto_reply = 1 OR ec.is_error_reply = 1) AND ec.email_seq_number = '2' THEN 1 ELSE 0 END AS replies_after_email_2,
    CASE WHEN (ec.is_reply = 1 OR ec.is_auto_reply = 1 OR ec.is_error_reply = 1) AND ec.email_seq_number = '3' THEN 1 ELSE 0 END AS replies_after_email_3,
    CASE WHEN (ec.is_reply = 1 OR ec.is_auto_reply = 1 OR ec.is_error_reply = 1) AND (ec.email_seq_number NOT IN ('1', '2', '3') OR ec.email_seq_number IS NULL) THEN 1 ELSE 0 END AS replies_after_email_unknown,
    
    -- MEETING METRICS: Set to 0 for email records
    0 AS meeting_slots_sent_after_1,
    0 AS meeting_slots_sent_after_2,
    0 AS meeting_slots_sent_after_3,
    0 AS meeting_slots_sent_unknown,
    0 AS meetings_booked,
    
    -- Record type identifier
    'EMAIL' as record_type

  FROM email_classification ec
  LEFT JOIN amazon_prospect_enrichment ape ON LOWER(TRIM(ec."threadToEmailId")) = LOWER(TRIM(ape.prospect_email))
  LEFT JOIN inbox_enrichment ie ON LOWER(TRIM(ec."threadStartEmailId")) = LOWER(TRIM(ie."inboxEmail"))
  LEFT JOIN campaign_parsing cp ON ec."campaingId" = cp."campaignId"
  LEFT JOIN client_info ci ON cp."clientId" = ci."clientId"

  UNION ALL

  -- MEETING RECORDS: Meeting metrics populated, email metrics = 0
  SELECT
    meetings."toEmailId" as prospect_email,
    meetings."fromEmailId" as inbox_email,
    -- Use daily date for temporal analysis (use original timestamp)
    DATE(meetings."date") as email_date,
    -- Week start date (Monday) for weekly analysis
    DATE_TRUNC('week', meetings."date")::date as week_start_date,
    
    -- Prospect Info
    COALESCE(ape.prospect_email_provider, 'UNK') as prospect_email_provider,
    COALESCE(ape.dominant_search_priority, 'UNK') as dominant_search_priority,
    COALESCE(ape.prospect_email_type, 'unknown') as prospect_email_type,
    COALESCE(ape.prospect_parent_provider, 'UNK') as prospect_parent_provider,
    
    -- Campaign Info
    COALESCE(cpu.campaign_series, 'UNK') as campaign_series,
    COALESCE(cpu.campaign_name, 'UNK') as campaign_name,
    COALESCE(cpu.jeff_campaign_code, meetings."funnel", 'UNK') as jeff_campaign_code,
    
    -- Inbox Provider Name
    ie.inbox_provider_name,
    ie.inbox_domain_name,
    ie.inbox_parent_provider,
    ie.tag_names,
    
    -- Client Name
    ci.client_name,
    
    -- Email sequence number (NULL for meeting records)
    NULL as email_seq_number,

    -- EMAIL METRICS: Set to 0 for meeting records
    0 AS email_1_sent,
    0 AS email_2_sent,
    0 AS email_3_sent,
    
    -- Email Sent Counts by Prospect Type
    0 AS email_1_sent_personal,
    0 AS email_1_sent_role,
    0 AS email_1_sent_work,
    0 AS email_1_sent_unknown,
    
    0 AS email_2_sent_personal,
    0 AS email_2_sent_role,
    0 AS email_2_sent_work,
    0 AS email_2_sent_unknown,
    
    0 AS email_3_sent_personal,
    0 AS email_3_sent_role,
    0 AS email_3_sent_work,
    0 AS email_3_sent_unknown,

    0 AS email_unknown_sent_personal,
    0 AS email_unknown_sent_role,
    0 AS email_unknown_sent_work,
    0 AS email_unknown_sent_unknown,
    
    -- Reply Counts
    0 AS replies_after_email_1,
    0 AS automated_replies_after_email_1,
    0 AS error_replies_after_email_1,
    
    0 AS replies_after_email_2,
    0 AS automated_replies_after_email_2,
    0 AS error_replies_after_email_2,
    
    0 AS replies_after_email_3,
    0 AS automated_replies_after_email_3,
    0 AS error_replies_after_email_3,
    
    0 AS replies_after_email_unknown,
    0 AS automated_replies_after_email_unknown,
    0 AS error_replies_after_email_unknown,
    
    -- Combined reply metrics (manual + auto + error) - Set to 0 for meeting records
    0 AS replies_after_email_1,
    0 AS replies_after_email_2,
    0 AS replies_after_email_3,
    0 AS replies_after_email_unknown,
    
    -- MEETING METRICS: Populated from meeting data
    meetings.slots_sent_after_1 AS meeting_slots_sent_after_1,
    meetings."slots_sent_after_2" AS meeting_slots_sent_after_2,
    meetings."slots_sent_after_3" AS meeting_slots_sent_after_3,
    meetings."slots_sent_unknown" AS meeting_slots_sent_unknown,
    meetings.booked AS meetings_booked,
    
    -- Record type identifier
    'MEETING' as record_type

  FROM "public2"."MeetingsFromReplit" AS meetings
  LEFT JOIN campaign_parsing_unique cpu ON meetings."client" = cpu."clientId" AND meetings."funnel" = cpu.jeff_campaign_code
  LEFT JOIN amazon_prospect_enrichment ape ON LOWER(TRIM(meetings."toEmailId")) = LOWER(TRIM(ape.prospect_email))
  LEFT JOIN inbox_enrichment ie ON LOWER(TRIM(meetings."fromEmailId")) = LOWER(TRIM(ie."inboxEmail"))
  LEFT JOIN client_info ci ON meetings."client" = ci."clientId"
)

-- FINAL AGGREGATED RESULT: Group by all dimensions and sum metrics
SELECT
  prospect_email,
  inbox_email,
  -- Temporal dimensions 
  email_date,
  week_start_date,
  
  -- Email sequence number (critical for proper aggregation)
  COALESCE(email_seq_number, 'UNK') as email_seq_number,
  
  -- Prospect dimensions (with UNK defaults for missing enrichment)
  COALESCE(prospect_email_provider, 'UNK') as prospect_email_provider,
  COALESCE(dominant_search_priority, 'UNK') as dominant_search_priority,
  COALESCE(prospect_parent_provider, 'UNK') as prospect_parent_provider,
  COALESCE(prospect_email_type, 'UNK') as prospect_email_type,
  
  -- Campaign dimensions (with UNK defaults for missing enrichment)
  COALESCE(campaign_series, 'UNK') as campaign_series,
  COALESCE(campaign_name, 'UNK') as campaign_name,
  COALESCE(jeff_campaign_code, 'UNK') as jeff_campaign_code,
  
  -- Inbox provider dimensions (with UNK defaults for missing enrichment)
  COALESCE(inbox_provider_name, 'UNK') as inbox_provider_name,
  COALESCE(inbox_domain_name, 'UNK') as inbox_domain_name,
  COALESCE(inbox_parent_provider, 'UNK') as inbox_parent_provider,
  COALESCE(tag_names, ARRAY[]::text[]) as tag_names,
  
  -- Client dimension (with UNK defaults for missing enrichment)
  COALESCE(client_name, 'UNK') as client_name,

  -- AGGREGATED EMAIL METRICS: Sum across all records for this dimension combination
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_1_sent ELSE 0 END) AS email_1_sent,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_2_sent ELSE 0 END) AS email_2_sent,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_3_sent ELSE 0 END) AS email_3_sent,
  
  -- Email Sent Counts by Prospect Type
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_1_sent_personal ELSE 0 END) AS email_1_sent_personal,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_1_sent_role ELSE 0 END) AS email_1_sent_role,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_1_sent_work ELSE 0 END) AS email_1_sent_work,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_1_sent_unknown ELSE 0 END) AS email_1_sent_unknown,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_2_sent_personal ELSE 0 END) AS email_2_sent_personal,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_2_sent_role ELSE 0 END) AS email_2_sent_role,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_2_sent_work ELSE 0 END) AS email_2_sent_work,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_2_sent_unknown ELSE 0 END) AS email_2_sent_unknown,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_3_sent_personal ELSE 0 END) AS email_3_sent_personal,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_3_sent_role ELSE 0 END) AS email_3_sent_role,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_3_sent_work ELSE 0 END) AS email_3_sent_work,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_3_sent_unknown ELSE 0 END) AS email_3_sent_unknown,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_unknown_sent_personal ELSE 0 END) AS email_unknown_sent_personal,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_unknown_sent_role ELSE 0 END) AS email_unknown_sent_role,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_unknown_sent_work ELSE 0 END) AS email_unknown_sent_work,
  SUM(CASE WHEN record_type = 'EMAIL' THEN email_unknown_sent_unknown ELSE 0 END) AS email_unknown_sent_unknown,
  
  -- Reply Counts with Proper Attribution
  SUM(CASE WHEN record_type = 'EMAIL' THEN replies_after_email_1 ELSE 0 END) AS replies_after_email_1,
  SUM(CASE WHEN record_type = 'EMAIL' THEN automated_replies_after_email_1 ELSE 0 END) AS automated_replies_after_email_1,
  SUM(CASE WHEN record_type = 'EMAIL' THEN error_replies_after_email_1 ELSE 0 END) AS error_replies_after_email_1,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN replies_after_email_2 ELSE 0 END) AS replies_after_email_2,
  SUM(CASE WHEN record_type = 'EMAIL' THEN automated_replies_after_email_2 ELSE 0 END) AS automated_replies_after_email_2,
  SUM(CASE WHEN record_type = 'EMAIL' THEN error_replies_after_email_2 ELSE 0 END) AS error_replies_after_email_2,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN replies_after_email_3 ELSE 0 END) AS replies_after_email_3,
  SUM(CASE WHEN record_type = 'EMAIL' THEN automated_replies_after_email_3 ELSE 0 END) AS automated_replies_after_email_3,
  SUM(CASE WHEN record_type = 'EMAIL' THEN error_replies_after_email_3 ELSE 0 END) AS error_replies_after_email_3,
  
  SUM(CASE WHEN record_type = 'EMAIL' THEN replies_after_email_unknown ELSE 0 END) AS replies_after_email_unknown,
  SUM(CASE WHEN record_type = 'EMAIL' THEN automated_replies_after_email_unknown ELSE 0 END) AS automated_replies_after_email_unknown,
  SUM(CASE WHEN record_type = 'EMAIL' THEN error_replies_after_email_unknown ELSE 0 END) AS error_replies_after_email_unknown,
  
  -- AGGREGATED MEETING METRICS: Sum across all records for this dimension combination
  SUM(CASE WHEN record_type = 'MEETING' THEN meeting_slots_sent_after_1 ELSE 0 END) AS meeting_slots_sent_after_1,
  SUM(CASE WHEN record_type = 'MEETING' THEN meeting_slots_sent_after_2 ELSE 0 END) AS meeting_slots_sent_after_2,
  SUM(CASE WHEN record_type = 'MEETING' THEN meeting_slots_sent_after_3 ELSE 0 END) AS meeting_slots_sent_after_3,
  SUM(CASE WHEN record_type = 'MEETING' THEN meeting_slots_sent_unknown ELSE 0 END) AS meeting_slots_sent_unknown,
  SUM(CASE WHEN record_type = 'MEETING' THEN meetings_booked ELSE 0 END) AS meetings_booked

FROM unified_data
GROUP BY 
  -- Group by all dimensions to ensure proper aggregation
  prospect_email,
  inbox_email,
  email_date,
  week_start_date,
  COALESCE(email_seq_number, 'UNK'),
  COALESCE(prospect_email_provider, 'UNK'),
  COALESCE(prospect_email_type, 'UNK'),
  COALESCE(dominant_search_priority, 'UNK'),
  COALESCE(prospect_parent_provider, 'UNK'),
  COALESCE(campaign_series, 'UNK'),
  COALESCE(campaign_name, 'UNK'),
  COALESCE(jeff_campaign_code, 'UNK'),
  COALESCE(inbox_provider_name, 'UNK'),
  COALESCE(inbox_domain_name, 'UNK'),
  COALESCE(inbox_parent_provider, 'UNK'),
  COALESCE(tag_names, ARRAY[]::text[]),
  COALESCE(client_name, 'UNK')
;

-- Indexes for optimal performance
-- Note: These indexes are created separately from the materialized view to avoid function context issues

-- Temporal analysis indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_prospect_email ON public.mv_lead_primary_flow_data(prospect_email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_inbox_email ON public.mv_lead_primary_flow_data(inbox_email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_email_date ON public.mv_lead_primary_flow_data(email_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_week_start_date ON public.mv_lead_primary_flow_data(week_start_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_date_client ON public.mv_lead_primary_flow_data(email_date, client_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_date_campaign ON public.mv_lead_primary_flow_data(email_date, jeff_campaign_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_date_provider ON public.mv_lead_primary_flow_data(email_date, inbox_provider_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_week_client ON public.mv_lead_primary_flow_data(week_start_date, client_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_week_campaign ON public.mv_lead_primary_flow_data(week_start_date, jeff_campaign_code);

-- Prospect dimensions 
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_prospect_email_provider ON public.mv_lead_primary_flow_data(prospect_email_provider);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_prospect_parent_provider ON public.mv_lead_primary_flow_data(prospect_parent_provider);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_dominant_search_priority ON public.mv_lead_primary_flow_data(dominant_search_priority);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_prospect_email_type ON public.mv_lead_primary_flow_data(prospect_email_type);

-- Campaign dimensions 
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_campaign_name ON public.mv_lead_primary_flow_data(campaign_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_jeff_campaign_code ON public.mv_lead_primary_flow_data(jeff_campaign_code);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_campaign_series ON public.mv_lead_primary_flow_data(campaign_series);

-- Inbox provider dimensions 
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_inbox_provider ON public.mv_lead_primary_flow_data(inbox_provider_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_inbox_domain ON public.mv_lead_primary_flow_data(inbox_domain_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_inbox_parent_provider ON public.mv_lead_primary_flow_data(inbox_parent_provider);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_tag_names ON public.mv_lead_primary_flow_data USING GIN(tag_names);

-- Client dimension 
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_client_name ON public.mv_lead_primary_flow_data(client_name);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_campaign_provider ON public.mv_lead_primary_flow_data(jeff_campaign_code, inbox_provider_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_primary_flow_data_client_campaign ON public.mv_lead_primary_flow_data(client_name, jeff_campaign_code);

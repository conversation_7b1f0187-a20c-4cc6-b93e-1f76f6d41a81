-- METABASE QUERY: Tag Analytics Dashboard with Lead Analytics Cube
-- Purpose: Tag-based performance analysis using mv_lead_analytics_cube

SELECT 
  week_start_date,
  inbox_provider_name as provider_name,
  inbox_parent_provider,
  client_name,
  funnel_code as campaign_code,
  unnest(tag_names) as tag_name,

  
  -- EMAIL PERFORMANCE BY PROVIDER
  SUM(email_1_sent) as email_1_sent,
  <PERSON>UM(replies_after_email_1) as replies_after_email_1,
  <PERSON><PERSON>(automated_replies_after_email_1) as automated_replies_after_email_1,
  <PERSON><PERSON>(error_replies_after_email_1) as error_replies_after_email_1,
  <PERSON><PERSON><PERSON>(email_1_reply_rate) as avg_email_1_reply_rate,
  
  <PERSON>UM(email_2_sent) as email_2_sent,
  <PERSON><PERSON>(replies_after_email_2) as replies_after_email_2,
  <PERSON><PERSON>(automated_replies_after_email_2) as automated_replies_after_email_2, 
  <PERSON>UM(error_replies_after_email_2) as error_replies_after_email_2,
  AV<PERSON>(email_2_reply_rate) as avg_email_2_reply_rate,
  
  SUM(email_3_sent) as email_3_sent,
  <PERSON><PERSON>(replies_after_email_3) as replies_after_email_3,
  <PERSON><PERSON>(automated_replies_after_email_3) as automated_replies_after_email_3,
  SUM(error_replies_after_email_3) as error_replies_after_email_3,
  AVG(email_3_reply_rate) as avg_email_3_reply_rate,
  
  -- PROVIDER RELIABILITY METRICS
  SUM(total_emails_sent) as total_emails_sent,
  SUM(total_replies) as total_replies,
  SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3) as total_error_replies,
  
  -- ERROR RATE BY PROVIDER
  CASE 
    WHEN SUM(total_emails_sent) > 0 
    THEN ROUND(100.0 * SUM(error_replies_after_email_1 + error_replies_after_email_2 + error_replies_after_email_3) / SUM(total_emails_sent), 2)
    ELSE 0 
  END as error_rate,
  
  -- OVERALL PERFORMANCE
  AVG(overall_reply_rate) as overall_reply_rate,
  SUM(unique_leads_contacted) as unique_leads_contacted,
  
  -- MEETING METRICS
  SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1,
  SUM(meeting_slots_sent_after_2) as meeting_slots_sent_after_2,
  SUM(meeting_slots_sent_after_3) as meeting_slots_sent_after_3,
  SUM(meeting_slots_sent_unknown) as meeting_slots_sent_unknown,
  SUM(meetings_booked) as meetings_booked,
  SUM(total_meeting_slots_sent) as total_meeting_slots_sent,
  AVG(booking_rate) as avg_booking_rate

FROM public.mv_lead_analytics_cube
  
GROUP BY 
  week_start_date, inbox_provider_name,
  inbox_parent_provider,
  client_name,
  funnel_code,
  tag_name
ORDER BY 
  week_start_date DESC, total_emails_sent DESC
-- ============================================================================
-- DATA ANOMALY DETECTION QUERIES
-- ============================================================================
-- These queries help identify data inconsistencies between meetings and email campaigns
-- that cause mismatches in the lead model vs non-orphaned meetings comparison.

-- ============================================================================
-- 1. OVERVIEW: Data Consistency Summary by Campaign
-- ============================================================================
-- Shows the percentage of meetings that have matching vs mismatched campaign codes
WITH meeting_email_analysis AS (
  SELECT 
    meetings."fromEmailId",
    meetings."toEmailId",
    meetings."date",
    meetings."funnel" as meeting_funnel,
    meetings."client" as meeting_client,
    meetings.booked,
    e."campaingId" as email_campaign_id,
    e."leadId",
    e."time" as email_time,
    cp.jeff_campaign_code as email_campaign_code,
    cp.campaign_name,
    CASE 
      WHEN meetings."funnel" = cp.jeff_campaign_code THEN 'MATCH'
      ELSE 'MISMATCH'
    END as consistency_status
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN (
    SELECT
      c."campaignId",
      c."clientId",
      c."name" as campaign_name,
      CASE
        WHEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
        THEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]')
        WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
        ELSE 'UNK'
      END AS jeff_campaign_code
    FROM public."Campaign" c
  ) cp ON e."campaingId" = cp."campaignId"
),
anomaly_summary AS (
  SELECT 
    meeting_funnel,
    consistency_status,
    COUNT(*) as meeting_count,
    SUM(booked) as booked_count,
    COUNT(DISTINCT meeting_client) as affected_clients,
    COUNT(DISTINCT email_campaign_id) as affected_campaigns
  FROM meeting_email_analysis
  GROUP BY meeting_funnel, consistency_status
)
SELECT 
  meeting_funnel,
  SUM(CASE WHEN consistency_status = 'MATCH' THEN meeting_count ELSE 0 END) as matching_meetings,
  SUM(CASE WHEN consistency_status = 'MATCH' THEN booked_count ELSE 0 END) as matching_booked,
  SUM(CASE WHEN consistency_status = 'MISMATCH' THEN meeting_count ELSE 0 END) as mismatched_meetings,
  SUM(CASE WHEN consistency_status = 'MISMATCH' THEN booked_count ELSE 0 END) as mismatched_booked,
  SUM(meeting_count) as total_meetings,
  SUM(booked_count) as total_booked,
  TRUNC(
    SUM(CASE WHEN consistency_status = 'MISMATCH' THEN meeting_count ELSE 0 END)::float / 
    SUM(meeting_count) * 100
  ) as mismatch_percentage
FROM anomaly_summary
GROUP BY meeting_funnel
ORDER BY mismatch_percentage DESC, total_meetings DESC;

-- ============================================================================
-- 2. DETAILED: Specific Anomaly Examples
-- ============================================================================
-- Shows specific examples of data inconsistencies with campaign details
WITH meeting_email_analysis AS (
  SELECT 
    meetings."fromEmailId",
    meetings."toEmailId",
    meetings."date",
    meetings."funnel" as meeting_funnel,
    meetings."client" as meeting_client,
    meetings.booked,
    e."campaingId" as email_campaign_id,
    e."leadId",
    e."time" as email_time,
    cp.jeff_campaign_code as email_campaign_code,
    cp.campaign_name,
    CASE 
      WHEN meetings."funnel" = cp.jeff_campaign_code THEN 'MATCH'
      ELSE 'MISMATCH'
    END as consistency_status
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN (
    SELECT
      c."campaignId",
      c."clientId",
      c."name" as campaign_name,
      CASE
        WHEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
        THEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]')
        WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
        ELSE 'UNK'
      END AS jeff_campaign_code
    FROM public."Campaign" c
  ) cp ON e."campaingId" = cp."campaignId"
)
SELECT 
  meeting_funnel,
  email_campaign_code,
  consistency_status,
  meeting_client,
  email_campaign_id,
  campaign_name,
  COUNT(*) as meeting_count,
  SUM(booked) as booked_count,
  MIN("date") as earliest_meeting,
  MAX("date") as latest_meeting
FROM meeting_email_analysis
WHERE consistency_status = 'MISMATCH'
GROUP BY meeting_funnel, email_campaign_code, consistency_status, meeting_client, email_campaign_id, campaign_name
ORDER BY meeting_count DESC
LIMIT 50;

-- ============================================================================
-- 3. IMPACT: Lead Model vs Non-Orphaned Meetings Comparison
-- ============================================================================
-- Shows the exact impact of data inconsistencies on model accuracy
WITH non_orphaned_meetings AS (
  SELECT 
    meetings."funnel",
    COUNT(*) as total_meetings,
    SUM(meetings.booked) as total_booked
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN (
    SELECT DISTINCT
      e."threadStartEmailId",
      e."threadToEmailId"
    FROM public.mv_email_with_thread_start e
  ) emails ON meetings."fromEmailId" = emails."threadStartEmailId" 
    AND meetings."toEmailId" = emails."threadToEmailId"
  WHERE meetings."funnel" IN ('6C', '6A', '10A', '7A', '7B', '8A', '6D', '10B', '21A')
  GROUP BY meetings."funnel"
),
lead_model_meetings AS (
  SELECT 
    jeff_campaign_code,
    COUNT(*) as total_meetings,
    SUM(meetings_booked) as total_booked
  FROM public.mv_lead_primary_flow_data
  WHERE jeff_campaign_code IN ('6C', '6A', '10A', '7A', '7B', '8A', '6D', '10B', '21A')
    AND meetings_date IS NOT NULL
  GROUP BY jeff_campaign_code
)
SELECT 
  n."funnel" as campaign_code,
  n.total_meetings as non_orphaned_meetings,
  n.total_booked as non_orphaned_booked,
  l.total_meetings as lead_model_meetings,
  l.total_booked as lead_model_booked,
  (n.total_meetings - l.total_meetings) as missing_meetings,
  (n.total_booked - l.total_booked) as missing_booked,
  CASE 
    WHEN n.total_meetings = l.total_meetings AND n.total_booked = l.total_booked 
    THEN 'EXACT_MATCH'
    ELSE 'MISMATCH'
  END as status
FROM non_orphaned_meetings n
LEFT JOIN lead_model_meetings l ON n."funnel" = l.jeff_campaign_code
ORDER BY missing_meetings DESC;

-- ============================================================================
-- 4. ROOT CAUSE: Campaign Mapping Issues
-- ============================================================================
-- Shows which campaigns are causing the most data inconsistencies
WITH campaign_mapping_issues AS (
  SELECT 
    meetings."funnel" as meeting_funnel,
    meetings."client" as meeting_client,
    e."campaingId" as email_campaign_id,
    cp.jeff_campaign_code as email_campaign_code,
    cp.campaign_name,
    COUNT(*) as meeting_count,
    SUM(meetings.booked) as booked_count
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN (
    SELECT
      c."campaignId",
      c."clientId",
      c."name" as campaign_name,
      CASE
        WHEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
        THEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]')
        WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
        ELSE 'UNK'
      END AS jeff_campaign_code
    FROM public."Campaign" c
  ) cp ON e."campaingId" = cp."campaignId"
  WHERE meetings."funnel" != cp.jeff_campaign_code
  GROUP BY meetings."funnel", meetings."client", e."campaingId", cp.jeff_campaign_code, cp.campaign_name
)
SELECT 
  meeting_funnel,
  email_campaign_code,
  meeting_client,
  email_campaign_id,
  campaign_name,
  meeting_count,
  booked_count
FROM campaign_mapping_issues
ORDER BY meeting_count DESC
LIMIT 30;

-- ============================================================================
-- 5. MONITORING: Daily Anomaly Detection
-- ============================================================================
-- Use this query to monitor new anomalies as they appear
WITH recent_anomalies AS (
  SELECT 
    meetings."fromEmailId",
    meetings."toEmailId",
    meetings."date",
    meetings."funnel" as meeting_funnel,
    meetings."client" as meeting_client,
    meetings.booked,
    e."campaingId" as email_campaign_id,
    cp.jeff_campaign_code as email_campaign_code,
    cp.campaign_name,
    CASE 
      WHEN meetings."funnel" = cp.jeff_campaign_code THEN 'MATCH'
      ELSE 'MISMATCH'
    END as consistency_status
  FROM "public2"."MeetingsFromReplit" meetings
  INNER JOIN public.mv_email_with_thread_start e ON meetings."fromEmailId" = e."threadStartEmailId" 
    AND meetings."toEmailId" = e."threadToEmailId"
  LEFT JOIN (
    SELECT
      c."campaignId",
      c."clientId",
      c."name" as campaign_name,
      CASE
        WHEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
        THEN SUBSTRING(c."name" FROM '[0-9]{1,2}[A-Z]')
        WHEN c."parentCampaignId" IS NOT NULL THEN 'HKP'
        ELSE 'UNK'
      END AS jeff_campaign_code
    FROM public."Campaign" c
  ) cp ON e."campaingId" = cp."campaignId"
  WHERE meetings."date" >= CURRENT_DATE - INTERVAL '7 days'  -- Last 7 days
)
SELECT 
  DATE("date") as meeting_date,
  meeting_funnel,
  email_campaign_code,
  consistency_status,
  COUNT(*) as meeting_count,
  SUM(booked) as booked_count
FROM recent_anomalies
GROUP BY DATE("date"), meeting_funnel, email_campaign_code, consistency_status
ORDER BY meeting_date DESC, meeting_count DESC;

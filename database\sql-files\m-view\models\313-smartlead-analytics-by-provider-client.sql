DROP MATERIALIZED VIEW IF EXISTS public.smartlead_analytics_by_provider_client;
CREATE MATERIALIZED VIEW public.smartlead_analytics_by_provider_client AS
WITH lead_email_flags AS (
  SELECT 
    "leadId",
    MAX(CASE WHEN "email_seq_number" = '1' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_email_1_sent,
    MAX(CASE WHEN "email_seq_number" = '2' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_email_2_sent,
    MAX(CASE WHEN "email_seq_number" = '3' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_email_3_sent
  FROM public.smartlead_emails_with_custom_reply_status
  GROUP BY "leadId"
),
enriched_emails AS (
  SELECT 
    emails.*,
    flags.has_email_1_sent,
    flags.has_email_2_sent,
    flags.has_email_3_sent,
    COALESCE(inboxes."provider_name", 'unknown_provider') AS provider_name
  FROM public.smartlead_emails_with_custom_reply_status AS emails
  LEFT JOIN lead_email_flags AS flags ON emails."leadId" = flags."leadId"
  LEFT JOIN "public2"."InboxesFromReplit" AS inboxes 
    ON emails."threadStartEmailId" = inboxes."inboxEmail"
)

SELECT 
  DATE(emails."time") AS email_date,
  DATE(DATE(emails."time") - EXTRACT(DOW FROM DATE(emails."time")) * INTERVAL '1 day') AS week_start_date,
  emails."jeff_client_name",
  emails."jeff_campaign_code" AS campaign_code,
  emails."clientSmartLeadId" AS client_id,
  emails.provider_name,
  
  -- EMAIL 1 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_1_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND emails.has_email_1_sent = 1
    THEN 1 
  END) AS replies_after_email_1_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND emails.has_email_1_sent = 1
    THEN 1 
  END) AS automated_replies_after_email_1_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND emails.has_email_1_sent = 1
    THEN 1 
  END) AS error_replies_after_email_1_count,

  -- EMAIL 2 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_2_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND emails.has_email_2_sent = 1
    THEN 1 
  END) AS replies_after_email_2_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND emails.has_email_2_sent = 1
    THEN 1 
  END) AS automated_replies_after_email_2_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND emails.has_email_2_sent = 1
    THEN 1 
  END) AS error_replies_after_email_2_count,

  -- EMAIL 3 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_3_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND emails.has_email_3_sent = 1
    THEN 1 
  END) AS replies_after_email_3_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND emails.has_email_3_sent = 1
    THEN 1 
  END) AS automated_replies_after_email_3_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND emails.has_email_3_sent = 1
    THEN 1 
  END) AS error_replies_after_email_3_count

FROM enriched_emails AS emails

GROUP BY 
  DATE(emails."time"),
  emails."jeff_client_name",
  emails."jeff_campaign_code",
  emails."clientSmartLeadId",
  emails.provider_name

ORDER BY 
  email_date DESC,
  emails."jeff_client_name",
  emails."jeff_campaign_code",
  client_id,
  provider_name
DROP MATERIALIZED VIEW IF EXISTS public.seller_group_sellers_with_sp_classifications_prospects_w_emails;
CREATE MATERIALIZED VIEW public.seller_group_sellers_with_sp_classifications_prospects_w_emails AS
Select
    sp.*,
    p.*
from
    public."SellerGroupSellersWithSearchPriorityExpanded" as sp
    JOIN public.seller_group_prospects_with_email_classification as p 
    ON sp."id" = p."seller_id"
where p.prospect_id is not null and p.email is not null
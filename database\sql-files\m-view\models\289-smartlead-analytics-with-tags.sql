DROP MATERIALIZED VIEW IF EXISTS public.smartlead_analytics_with_tags;
CREATE MATERIALIZED VIEW public.smartlead_analytics_with_tags AS
-- Optimized version - eliminates repeated EXISTS subqueries
WITH email_sent_flags AS (
  -- Pre-calculate which leads have sent each email sequence
  SELECT 
    "leadId",
    MAX(CASE WHEN "email_seq_number" = '1' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_1,
    MAX(CASE WHEN "email_seq_number" = '2' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_2,
    MAX(CASE WHEN "email_seq_number" = '3' AND "type" = 'SENT' THEN 1 ELSE 0 END) AS has_sent_email_3
  FROM public.smartlead_emails_with_custom_reply_status
  WHERE "type" = 'SENT' AND "email_seq_number" IN ('1', '2', '3')
  GROUP BY "leadId"
),
meeting_agg AS (
  -- Pre-aggregate meeting data to avoid repeated DISTINCT operations
  SELECT 
    "client",
    "date", 
    "funnel",
    "fromEmailId",
    SUM("slots_sent_after_1") AS total_slots_sent_after_1,
    SUM("slots_sent_after_2") AS total_slots_sent_after_2,
    SUM("slots_sent_after_3") AS total_slots_sent_after_3,
    SUM("slots_sent_unknown") AS total_slots_sent_unknown,
    SUM("booked") AS total_booked
  FROM "public2"."MeetingsFromReplit"
  GROUP BY "client", "date", "funnel", "fromEmailId"
)

SELECT 
  DATE(emails."time") AS email_date,
  DATE(DATE(emails."time") - EXTRACT(DOW FROM DATE(emails."time")) * INTERVAL '1 day') AS week_start_date,
  emails."jeff_client_name",
  emails."jeff_campaign_code" AS campaign_code,
  emails."clientSmartLeadId" AS client_id,
  COALESCE(inboxes."provider_name", 'unknown_provider') AS provider_name,
  COALESCE(tags.tag, 'no_tag') AS tag_name,
  
  -- EMAIL 1 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '1' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_1_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND flags.has_sent_email_1 = 1
    THEN 1 
  END) AS replies_after_email_1_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_1 = 1
    THEN 1 
  END) AS automated_replies_after_email_1_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND flags.has_sent_email_1 = 1
    THEN 1 
  END) AS error_replies_after_email_1_count,

  COALESCE(MAX(meeting_agg.total_slots_sent_after_1), 0) AS meeting_slots_sent_after_1,

  -- EMAIL 2 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '2' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_2_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND flags.has_sent_email_2 = 1
    THEN 1 
  END) AS replies_after_email_2_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_2 = 1
    THEN 1 
  END) AS automated_replies_after_email_2_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND flags.has_sent_email_2 = 1
    THEN 1 
  END) AS error_replies_after_email_2_count,

  COALESCE(MAX(meeting_agg.total_slots_sent_after_2), 0) AS meeting_slots_sent_after_2,

  -- EMAIL 3 ANALYTICS
  COUNT(CASE 
    WHEN emails."email_seq_number" = '3' AND emails."type" = 'SENT' 
    THEN 1 
  END) AS email_3_sent_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY' AND flags.has_sent_email_3 = 1
    THEN 1 
  END) AS replies_after_email_3_count,
  
  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'REPLY_CS_AUTOMATED' AND flags.has_sent_email_3 = 1
    THEN 1 
  END) AS automated_replies_after_email_3_count,

  COUNT(CASE 
    WHEN emails."jeff_email_status" = 'ERROR_REPLY' AND flags.has_sent_email_3 = 1
    THEN 1 
  END) AS error_replies_after_email_3_count,

  COALESCE(MAX(meeting_agg.total_slots_sent_after_3), 0) AS meeting_slots_sent_after_3,

  -- ADDITIONAL MEETING ANALYTICS
  COALESCE(MAX(meeting_agg.total_slots_sent_unknown), 0) AS meeting_slots_sent_unknown,
  
  COALESCE(MAX(meeting_agg.total_slots_sent_after_1 + meeting_agg.total_slots_sent_after_2 + 
         meeting_agg.total_slots_sent_after_3 + meeting_agg.total_slots_sent_unknown), 0) AS total_meeting_slots_sent,
  
  COALESCE(MAX(meeting_agg.total_booked), 0) AS meetings_booked

FROM public.smartlead_emails_with_custom_reply_status AS emails

-- Join the pre-calculated flags
LEFT JOIN email_sent_flags AS flags 
  ON emails."leadId" = flags."leadId"

-- Join to get inbox information
LEFT JOIN "public2"."InboxesFromReplit" AS inboxes 
  ON emails."threadStartEmailId" = inboxes."inboxEmail"

-- Join to get inbox tags
LEFT JOIN "public2"."InboxTagsFromReplit" AS tags 
  ON inboxes."id" = tags."inboxId"

-- Join to get pre-aggregated meeting data
LEFT JOIN meeting_agg 
  ON emails."clientSmartLeadId" = meeting_agg."client" 
  AND DATE(emails."time") = meeting_agg."date"
  AND emails."jeff_campaign_code" = meeting_agg."funnel"
  AND emails."threadStartEmailId" = meeting_agg."fromEmailId"

GROUP BY 
  DATE(emails."time"),
  emails."jeff_client_name",
  emails."jeff_campaign_code",
  emails."clientSmartLeadId",
  inboxes."provider_name",
  tags.tag

ORDER BY 
  email_date DESC,
  emails."jeff_client_name",
  emails."jeff_campaign_code",
  client_id,
  provider_name,
  tag_name
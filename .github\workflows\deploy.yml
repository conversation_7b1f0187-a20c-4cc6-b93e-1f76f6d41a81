name: Deploy to Azure VM

on:
  push:
    branches:
      - main # Or your deployment branch

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
      - name: SSH and Restart Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            echo "🚀 Triggering service restart to deploy new code..."
            sudo systemctl restart santra.service

            echo "⏳ Waiting for service to initialize..."
            sleep 15

            echo "🔎 Checking deployment status..."
            # Check the final status and fail the job if the service is not active
            # This provides clear feedback on whether the deployment succeeded
            sudo systemctl is-active --quiet santra.service || (sudo systemctl status santra.service --no-pager && exit 1)

            echo "✅ Deployment successful. Displaying recent logs..."
            sudo journalctl -u santra.service -n 50 --no-pager
SELECT 
  week_start_date,
  search_priority,
  client_name,
  funnel_code,
  tag_name,
  campaign_name,
  inbox_provider_name,
  prospect_email_provider,
  inbox_parent_provider,
  prospect_parent_provider,
  inbox_domain_name,
  -- LEAD QUALITY METRICS
  SUM(unique_leads_contacted) as unique_leads_contacted,
  <PERSON>UM(total_emails_sent) as total_emails_sent,
  SUM(total_replies) as total_replies,
  
  -- PERFORMANCE BY EMAIL SEQUENCE
  SUM(email_1_sent) as email_1_sent,
  SUM(replies_after_email_1) as replies_after_email_1,
  AV<PERSON>(email_1_reply_rate) as avg_email_1_reply_rate,
  
  SUM(email_2_sent) as email_2_sent,
  <PERSON>UM(replies_after_email_2) as replies_after_email_2,
  AV<PERSON>(email_2_reply_rate) as avg_email_2_reply_rate,
  
  SUM(email_3_sent) as email_3_sent,
  SUM(replies_after_email_3) as replies_after_email_3,
  AVG(email_3_reply_rate) as avg_email_3_reply_rate,
  
  -- MEETING CONVERSION BY PRIORITY
  SUM(meeting_slots_sent_after_1) as meeting_slots_sent_after_1,
  <PERSON><PERSON>(meeting_slots_sent_after_2) as meeting_slots_sent_after_2,
  SUM(meeting_slots_sent_after_3) as meeting_slots_sent_after_3,
  SUM(meeting_slots_sent_unknown) as meeting_slots_sent_unknown,
  SUM(meetings_booked) as meetings_booked,
  SUM(total_meeting_slots_sent) as total_meeting_slots_sent,
  AVG(booking_rate) as avg_booking_rate,
  
  -- EFFICIENCY METRICS
  AVG(overall_reply_rate) as overall_reply_rate,
  CASE 
    WHEN SUM(unique_leads_contacted) > 0 
    THEN ROUND(SUM(meetings_booked)::decimal / SUM(unique_leads_contacted) * 100, 2)
    ELSE 0 
  END as lead_to_meeting_conversion_rate

FROM public.mv_analytics_cube_9d
GROUP BY 
  week_start_date, search_priority, client_name, funnel_code, tag_name, campaign_name,
  inbox_provider_name, prospect_email_provider, inbox_parent_provider, prospect_parent_provider, inbox_domain_name
ORDER BY 
  week_start_date DESC, search_priority, total_emails_sent DESC